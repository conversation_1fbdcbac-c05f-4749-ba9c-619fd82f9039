<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net46</TargetFramework>
    <AssemblyName>MyThirdPluginCollision</AssemblyName>
    <Product>My third plugin</Product>
    <Version>1.0.0</Version>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <LangVersion>latest</LangVersion>
    <RestoreAdditionalProjectSources>
      https://api.nuget.org/v3/index.json;
      https://nuget.bepinex.dev/v3/index.json;
      https://nuget.samboy.dev/v3/index.json
    </RestoreAdditionalProjectSources>
    <RootNamespace>MyThirdPluginCollision</RootNamespace>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="BepInEx.Analyzers" Version="1.*" PrivateAssets="all" />
    <PackageReference Include="BepInEx.Core" Version="5.*" />
    <PackageReference Include="BepInEx.PluginInfoProps" Version="2.*" />
    <PackageReference Include="UnityEngine.Modules" Version="2018.4.11" IncludeAssets="compile" />
    
    <Reference Include="E:\Plugins DEV\include\UnityEngine.UI">
      <HintPath>E:\Plugins DEV\include\UnityEngine.UI.dll</HintPath>
    </Reference>
    <Reference Include="E:\Plugins DEV\include\Assembly-CSharp">
      <HintPath>E:\Plugins DEV\include\Assembly-CSharp.dll</HintPath>
    </Reference>
    <Reference Include="E:\Plugins DEV\include\UniRx">
      <HintPath>E:\Plugins DEV\include\UniRx.dll</HintPath>
    </Reference>
    <Reference Include="E:\Plugins DEV\include\IL">
      <HintPath>E:\Plugins DEV\include\IL.dll</HintPath>
    </Reference>
    
  </ItemGroup>
  
  <ItemGroup Condition="'$(TargetFramework.TrimEnd(`0123456789`))' == 'net'">
    <PackageReference Include="Microsoft.NETFramework.ReferenceAssemblies" Version="1.0.2" PrivateAssets="all" />
  </ItemGroup>
</Project>
