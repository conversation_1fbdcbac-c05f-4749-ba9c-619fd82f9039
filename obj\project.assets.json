{"version": 3, "targets": {".NETFramework,Version=v4.6": {"BepInEx.Analyzers/1.0.8": {"type": "package"}, "BepInEx.BaseLib/5.4.20": {"type": "package", "compile": {"lib/net35/BepInEx.dll": {}}, "runtime": {"lib/net35/BepInEx.dll": {}}}, "BepInEx.Core/5.4.21": {"type": "package", "dependencies": {"BepInEx.BaseLib": "5.4.20", "HarmonyX": "2.7.0"}, "compile": {"lib/net35/_._": {}}, "runtime": {"lib/net35/_._": {}}, "build": {"build/BepInEx.Core.targets": {}}}, "BepInEx.PluginInfoProps/2.1.0": {"type": "package", "build": {"build/BepInEx.PluginInfoProps.props": {}}}, "HarmonyX/2.7.0": {"type": "package", "dependencies": {"MonoMod.RuntimeDetour": "21.12.13.1"}, "compile": {"lib/net45/0Harmony.dll": {"related": ".xml"}}, "runtime": {"lib/net45/0Harmony.dll": {"related": ".xml"}}}, "Microsoft.NETFramework.ReferenceAssemblies/1.0.2": {"type": "package", "dependencies": {"Microsoft.NETFramework.ReferenceAssemblies.net46": "1.0.2"}}, "Microsoft.NETFramework.ReferenceAssemblies.net46/1.0.2": {"type": "package", "build": {"build/Microsoft.NETFramework.ReferenceAssemblies.net46.targets": {}}}, "Mono.Cecil/0.11.4": {"type": "package", "compile": {"lib/net40/Mono.Cecil.Mdb.dll": {"related": ".pdb"}, "lib/net40/Mono.Cecil.Pdb.dll": {"related": ".pdb"}, "lib/net40/Mono.Cecil.Rocks.dll": {"related": ".pdb"}, "lib/net40/Mono.Cecil.dll": {"related": ".Mdb.pdb;.pdb;.Pdb.pdb;.Rocks.pdb"}}, "runtime": {"lib/net40/Mono.Cecil.Mdb.dll": {"related": ".pdb"}, "lib/net40/Mono.Cecil.Pdb.dll": {"related": ".pdb"}, "lib/net40/Mono.Cecil.Rocks.dll": {"related": ".pdb"}, "lib/net40/Mono.Cecil.dll": {"related": ".Mdb.pdb;.pdb;.Pdb.pdb;.Rocks.pdb"}}}, "MonoMod.RuntimeDetour/21.12.13.1": {"type": "package", "dependencies": {"Mono.Cecil": "0.11.4", "MonoMod.Utils": "21.12.13.1"}, "frameworkAssemblies": ["System"], "compile": {"lib/net452/MonoMod.RuntimeDetour.dll": {"related": ".xml"}}, "runtime": {"lib/net452/MonoMod.RuntimeDetour.dll": {"related": ".xml"}}}, "MonoMod.Utils/21.12.13.1": {"type": "package", "dependencies": {"Mono.Cecil": "0.11.4"}, "frameworkAssemblies": ["System"], "compile": {"lib/net452/MonoMod.Utils.dll": {"related": ".xml"}}, "runtime": {"lib/net452/MonoMod.Utils.dll": {"related": ".xml"}}}, "UnityEngine.Modules/2018.4.11": {"type": "package", "compile": {"lib/net45/UnityEngine.AIModule.dll": {}, "lib/net45/UnityEngine.ARModule.dll": {}, "lib/net45/UnityEngine.AccessibilityModule.dll": {}, "lib/net45/UnityEngine.AnimationModule.dll": {}, "lib/net45/UnityEngine.AssetBundleModule.dll": {}, "lib/net45/UnityEngine.AudioModule.dll": {}, "lib/net45/UnityEngine.BaselibModule.dll": {}, "lib/net45/UnityEngine.ClothModule.dll": {}, "lib/net45/UnityEngine.ClusterInputModule.dll": {}, "lib/net45/UnityEngine.ClusterRendererModule.dll": {}, "lib/net45/UnityEngine.CoreModule.dll": {}, "lib/net45/UnityEngine.CrashReportingModule.dll": {}, "lib/net45/UnityEngine.DirectorModule.dll": {}, "lib/net45/UnityEngine.FileSystemHttpModule.dll": {}, "lib/net45/UnityEngine.GameCenterModule.dll": {}, "lib/net45/UnityEngine.GridModule.dll": {}, "lib/net45/UnityEngine.HotReloadModule.dll": {}, "lib/net45/UnityEngine.IMGUIModule.dll": {}, "lib/net45/UnityEngine.ImageConversionModule.dll": {}, "lib/net45/UnityEngine.InputModule.dll": {}, "lib/net45/UnityEngine.JSONSerializeModule.dll": {}, "lib/net45/UnityEngine.LocalizationModule.dll": {}, "lib/net45/UnityEngine.ParticleSystemModule.dll": {}, "lib/net45/UnityEngine.PerformanceReportingModule.dll": {}, "lib/net45/UnityEngine.Physics2DModule.dll": {}, "lib/net45/UnityEngine.PhysicsModule.dll": {}, "lib/net45/UnityEngine.ProfilerModule.dll": {}, "lib/net45/UnityEngine.ScreenCaptureModule.dll": {}, "lib/net45/UnityEngine.SharedInternalsModule.dll": {}, "lib/net45/UnityEngine.SpriteMaskModule.dll": {}, "lib/net45/UnityEngine.SpriteShapeModule.dll": {}, "lib/net45/UnityEngine.StreamingModule.dll": {}, "lib/net45/UnityEngine.StyleSheetsModule.dll": {}, "lib/net45/UnityEngine.SubstanceModule.dll": {}, "lib/net45/UnityEngine.TLSModule.dll": {}, "lib/net45/UnityEngine.TerrainModule.dll": {}, "lib/net45/UnityEngine.TerrainPhysicsModule.dll": {}, "lib/net45/UnityEngine.TextCoreModule.dll": {}, "lib/net45/UnityEngine.TextRenderingModule.dll": {}, "lib/net45/UnityEngine.TilemapModule.dll": {}, "lib/net45/UnityEngine.TimelineModule.dll": {}, "lib/net45/UnityEngine.UIElementsModule.dll": {}, "lib/net45/UnityEngine.UIModule.dll": {}, "lib/net45/UnityEngine.UNETModule.dll": {}, "lib/net45/UnityEngine.UmbraModule.dll": {}, "lib/net45/UnityEngine.UnityAnalyticsModule.dll": {}, "lib/net45/UnityEngine.UnityConnectModule.dll": {}, "lib/net45/UnityEngine.UnityTestProtocolModule.dll": {}, "lib/net45/UnityEngine.UnityWebRequestAssetBundleModule.dll": {}, "lib/net45/UnityEngine.UnityWebRequestAudioModule.dll": {}, "lib/net45/UnityEngine.UnityWebRequestModule.dll": {}, "lib/net45/UnityEngine.UnityWebRequestTextureModule.dll": {}, "lib/net45/UnityEngine.UnityWebRequestWWWModule.dll": {}, "lib/net45/UnityEngine.VFXModule.dll": {}, "lib/net45/UnityEngine.VRModule.dll": {}, "lib/net45/UnityEngine.VehiclesModule.dll": {}, "lib/net45/UnityEngine.VideoModule.dll": {}, "lib/net45/UnityEngine.WindModule.dll": {}, "lib/net45/UnityEngine.XRModule.dll": {}, "lib/net45/UnityEngine.dll": {}}, "runtime": {"lib/net45/_._": {}}}}}, "libraries": {"BepInEx.Analyzers/1.0.8": {"sha512": "xrfNmunsPhBx+vStTxLonq/aHkRrDH77c9tG/x3m5eejrKe5B0nf7cJPRRt6x330sGI0bLaPTtygdeHUgvI3wQ==", "type": "package", "path": "bepinex.analyzers/1.0.8", "hasTools": true, "files": [".nupkg.metadata", "analyzers/dotnet/cs/BepInEx.Analyzers.CodeFixes.dll", "analyzers/dotnet/cs/BepInEx.Analyzers.dll", "bepinex.analyzers.1.0.8.nupkg.sha512", "bepinex.analyzers.nuspec", "tools/install.ps1", "tools/uninstall.ps1"]}, "BepInEx.BaseLib/5.4.20": {"sha512": "0bXgYxbCEN2Ixp3kiFEhyw+RASeFQeg/ww+lbMt7if6XMeVS60eg6epNsMA8Jbx57dmNOzNevkKKw8mP8SUMqw==", "type": "package", "path": "bepinex.baselib/5.4.20", "files": [".nupkg.metadata", "bepinex.baselib.5.4.20.nupkg.sha512", "bepinex.baselib.nuspec", "images/icon.png", "lib/net35/BepInEx.dll", "lib/netstandard2.0/BepInEx.dll"]}, "BepInEx.Core/5.4.21": {"sha512": "NMUPlbHTTfJ+qIQCI90uIvjuUQ4wnwt4cpRsK3ItBh1DhsWFzAHXNiZjBxZkPysljEKQ2iu89sxMTga4bxBXVQ==", "type": "package", "path": "bepinex.core/5.4.21", "hasTools": true, "files": [".nupkg.metadata", "bepinex.core.5.4.21.nupkg.sha512", "bepinex.core.nuspec", "build/BepInEx.Core.targets", "images/icon.png", "lib/net35/_._", "lib/netstandard2.0/_._", "tools/Install.ps1"]}, "BepInEx.PluginInfoProps/2.1.0": {"sha512": "VCG3QRiqNdW9ku2FEcNsbK+HKxrZmW0VxwMNyLNO7h0xGorU+C6vBHN8Qq4eAL5fU11Uks5x2uoYdqEoKD3P8A==", "type": "package", "path": "bepinex.plugininfoprops/2.1.0", "files": [".nupkg.metadata", "bepinex.plugininfoprops.2.1.0.nupkg.sha512", "bepinex.plugininfoprops.nuspec", "build/BepInEx.PluginInfoProps.props"]}, "HarmonyX/2.7.0": {"sha512": "lec/SkduQspMa3Pi/nM/NSvA84Za8HCCWA8ybdToKiTqnBZa+JC5g6rxoFQCg/1vNuYcvjy77edePZzIEsRmvw==", "type": "package", "path": "harmonyx/2.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "harmonyx.2.7.0.nupkg.sha512", "harmonyx.nuspec", "lib/net35/0Harmony.dll", "lib/net35/0Harmony.xml", "lib/net45/0Harmony.dll", "lib/net45/0Harmony.xml", "lib/netstandard2.0/0Harmony.dll", "lib/netstandard2.0/0Harmony.xml", "logo_mini.png"]}, "Microsoft.NETFramework.ReferenceAssemblies/1.0.2": {"sha512": "5/cSEVld+px/CuRrbohO/djfg6++eR6zGpy88MgqloXvkj//WXWpFZyu/OpkXPN0u5m+dN/EVwLNYFUxD4h2+A==", "type": "package", "path": "microsoft.netframework.referenceassemblies/1.0.2", "files": [".nupkg.metadata", ".signature.p7s", "microsoft.netframework.referenceassemblies.1.0.2.nupkg.sha512", "microsoft.netframework.referenceassemblies.nuspec"]}, "Microsoft.NETFramework.ReferenceAssemblies.net46/1.0.2": {"sha512": "ja03PYUrlY0wDsqAanq+r9vOnuYR5n2pd5S/sIDtC6v+BUvd34ftz0ayAb7F72lduT4qCSKtkS2EZC80/NBNMA==", "type": "package", "path": "microsoft.netframework.referenceassemblies.net46/1.0.2", "files": [".nupkg.metadata", ".signature.p7s", "build/.NETFramework/v4.6/Accessibility.dll", "build/.NETFramework/v4.6/Accessibility.xml", "build/.NETFramework/v4.6/CustomMarshalers.dll", "build/.NETFramework/v4.6/CustomMarshalers.xml", "build/.NETFramework/v4.6/Facades/System.Collections.Concurrent.dll", "build/.NETFramework/v4.6/Facades/System.Collections.dll", "build/.NETFramework/v4.6/Facades/System.ComponentModel.Annotations.dll", "build/.NETFramework/v4.6/Facades/System.ComponentModel.EventBasedAsync.dll", "build/.NETFramework/v4.6/Facades/System.ComponentModel.dll", "build/.NETFramework/v4.6/Facades/System.Diagnostics.Contracts.dll", "build/.NETFramework/v4.6/Facades/System.Diagnostics.Debug.dll", "build/.NETFramework/v4.6/Facades/System.Diagnostics.Tools.dll", "build/.NETFramework/v4.6/Facades/System.Diagnostics.Tracing.dll", "build/.NETFramework/v4.6/Facades/System.Dynamic.Runtime.dll", "build/.NETFramework/v4.6/Facades/System.Globalization.dll", "build/.NETFramework/v4.6/Facades/System.IO.dll", "build/.NETFramework/v4.6/Facades/System.Linq.Expressions.dll", "build/.NETFramework/v4.6/Facades/System.Linq.Parallel.dll", "build/.NETFramework/v4.6/Facades/System.Linq.Queryable.dll", "build/.NETFramework/v4.6/Facades/System.Linq.dll", "build/.NETFramework/v4.6/Facades/System.Net.NetworkInformation.dll", "build/.NETFramework/v4.6/Facades/System.Net.Primitives.dll", "build/.NETFramework/v4.6/Facades/System.Net.Requests.dll", "build/.NETFramework/v4.6/Facades/System.Net.WebHeaderCollection.dll", "build/.NETFramework/v4.6/Facades/System.ObjectModel.dll", "build/.NETFramework/v4.6/Facades/System.Reflection.Emit.ILGeneration.dll", "build/.NETFramework/v4.6/Facades/System.Reflection.Emit.Lightweight.dll", "build/.NETFramework/v4.6/Facades/System.Reflection.Emit.dll", "build/.NETFramework/v4.6/Facades/System.Reflection.Extensions.dll", "build/.NETFramework/v4.6/Facades/System.Reflection.Primitives.dll", "build/.NETFramework/v4.6/Facades/System.Reflection.dll", "build/.NETFramework/v4.6/Facades/System.Resources.ResourceManager.dll", "build/.NETFramework/v4.6/Facades/System.Runtime.Extensions.dll", "build/.NETFramework/v4.6/Facades/System.Runtime.Handles.dll", "build/.NETFramework/v4.6/Facades/System.Runtime.InteropServices.WindowsRuntime.dll", "build/.NETFramework/v4.6/Facades/System.Runtime.InteropServices.dll", "build/.NETFramework/v4.6/Facades/System.Runtime.Numerics.dll", "build/.NETFramework/v4.6/Facades/System.Runtime.Serialization.Json.dll", "build/.NETFramework/v4.6/Facades/System.Runtime.Serialization.Primitives.dll", "build/.NETFramework/v4.6/Facades/System.Runtime.Serialization.Xml.dll", "build/.NETFramework/v4.6/Facades/System.Runtime.dll", "build/.NETFramework/v4.6/Facades/System.Security.Principal.dll", "build/.NETFramework/v4.6/Facades/System.ServiceModel.Duplex.dll", "build/.NETFramework/v4.6/Facades/System.ServiceModel.Http.dll", "build/.NETFramework/v4.6/Facades/System.ServiceModel.NetTcp.dll", "build/.NETFramework/v4.6/Facades/System.ServiceModel.Primitives.dll", "build/.NETFramework/v4.6/Facades/System.ServiceModel.Security.dll", "build/.NETFramework/v4.6/Facades/System.Text.Encoding.Extensions.dll", "build/.NETFramework/v4.6/Facades/System.Text.Encoding.dll", "build/.NETFramework/v4.6/Facades/System.Text.RegularExpressions.dll", "build/.NETFramework/v4.6/Facades/System.Threading.Tasks.Parallel.dll", "build/.NETFramework/v4.6/Facades/System.Threading.Tasks.dll", "build/.NETFramework/v4.6/Facades/System.Threading.Timer.dll", "build/.NETFramework/v4.6/Facades/System.Threading.dll", "build/.NETFramework/v4.6/Facades/System.Xml.ReaderWriter.dll", "build/.NETFramework/v4.6/Facades/System.Xml.XDocument.dll", "build/.NETFramework/v4.6/Facades/System.Xml.XmlSerializer.dll", "build/.NETFramework/v4.6/ISymWrapper.dll", "build/.NETFramework/v4.6/ISymWrapper.xml", "build/.NETFramework/v4.6/Microsoft.Activities.Build.dll", "build/.NETFramework/v4.6/Microsoft.Activities.Build.xml", "build/.NETFramework/v4.6/Microsoft.Build.Conversion.v4.0.dll", "build/.NETFramework/v4.6/Microsoft.Build.Conversion.v4.0.xml", "build/.NETFramework/v4.6/Microsoft.Build.Engine.dll", "build/.NETFramework/v4.6/Microsoft.Build.Engine.xml", "build/.NETFramework/v4.6/Microsoft.Build.Framework.dll", "build/.NETFramework/v4.6/Microsoft.Build.Framework.xml", "build/.NETFramework/v4.6/Microsoft.Build.Tasks.v4.0.dll", "build/.NETFramework/v4.6/Microsoft.Build.Tasks.v4.0.xml", "build/.NETFramework/v4.6/Microsoft.Build.Utilities.v4.0.dll", "build/.NETFramework/v4.6/Microsoft.Build.Utilities.v4.0.xml", "build/.NETFramework/v4.6/Microsoft.Build.dll", "build/.NETFramework/v4.6/Microsoft.Build.xml", "build/.NETFramework/v4.6/Microsoft.CSharp.dll", "build/.NETFramework/v4.6/Microsoft.CSharp.xml", "build/.NETFramework/v4.6/Microsoft.JScript.dll", "build/.NETFramework/v4.6/Microsoft.JScript.xml", "build/.NETFramework/v4.6/Microsoft.VisualBasic.Compatibility.Data.dll", "build/.NETFramework/v4.6/Microsoft.VisualBasic.Compatibility.Data.xml", "build/.NETFramework/v4.6/Microsoft.VisualBasic.Compatibility.dll", "build/.NETFramework/v4.6/Microsoft.VisualBasic.Compatibility.xml", "build/.NETFramework/v4.6/Microsoft.VisualBasic.dll", "build/.NETFramework/v4.6/Microsoft.VisualBasic.xml", "build/.NETFramework/v4.6/Microsoft.VisualC.STLCLR.dll", "build/.NETFramework/v4.6/Microsoft.VisualC.STLCLR.xml", "build/.NETFramework/v4.6/Microsoft.VisualC.dll", "build/.NETFramework/v4.6/Microsoft.VisualC.xml", "build/.NETFramework/v4.6/PermissionSets/FullTrust.xml", "build/.NETFramework/v4.6/PermissionSets/Internet.xml", "build/.NETFramework/v4.6/PermissionSets/LocalIntranet.xml", "build/.NETFramework/v4.6/PresentationBuildTasks.dll", "build/.NETFramework/v4.6/PresentationBuildTasks.xml", "build/.NETFramework/v4.6/PresentationCore.dll", "build/.NETFramework/v4.6/PresentationCore.xml", "build/.NETFramework/v4.6/PresentationFramework.Aero.dll", "build/.NETFramework/v4.6/PresentationFramework.Aero.xml", "build/.NETFramework/v4.6/PresentationFramework.Aero2.dll", "build/.NETFramework/v4.6/PresentationFramework.Aero2.xml", "build/.NETFramework/v4.6/PresentationFramework.AeroLite.dll", "build/.NETFramework/v4.6/PresentationFramework.AeroLite.xml", "build/.NETFramework/v4.6/PresentationFramework.Classic.dll", "build/.NETFramework/v4.6/PresentationFramework.Classic.xml", "build/.NETFramework/v4.6/PresentationFramework.Luna.dll", "build/.NETFramework/v4.6/PresentationFramework.Luna.xml", "build/.NETFramework/v4.6/PresentationFramework.Royale.dll", "build/.NETFramework/v4.6/PresentationFramework.Royale.xml", "build/.NETFramework/v4.6/PresentationFramework.dll", "build/.NETFramework/v4.6/PresentationFramework.xml", "build/.NETFramework/v4.6/ReachFramework.dll", "build/.NETFramework/v4.6/ReachFramework.xml", "build/.NETFramework/v4.6/RedistList/FrameworkList.xml", "build/.NETFramework/v4.6/System.Activities.Core.Presentation.dll", "build/.NETFramework/v4.6/System.Activities.Core.Presentation.xml", "build/.NETFramework/v4.6/System.Activities.DurableInstancing.dll", "build/.NETFramework/v4.6/System.Activities.DurableInstancing.xml", "build/.NETFramework/v4.6/System.Activities.Presentation.dll", "build/.NETFramework/v4.6/System.Activities.Presentation.xml", "build/.NETFramework/v4.6/System.Activities.dll", "build/.NETFramework/v4.6/System.Activities.xml", "build/.NETFramework/v4.6/System.AddIn.Contract.dll", "build/.NETFramework/v4.6/System.AddIn.Contract.xml", "build/.NETFramework/v4.6/System.AddIn.dll", "build/.NETFramework/v4.6/System.AddIn.xml", "build/.NETFramework/v4.6/System.ComponentModel.Composition.Registration.dll", "build/.NETFramework/v4.6/System.ComponentModel.Composition.Registration.xml", "build/.NETFramework/v4.6/System.ComponentModel.Composition.dll", "build/.NETFramework/v4.6/System.ComponentModel.Composition.xml", "build/.NETFramework/v4.6/System.ComponentModel.DataAnnotations.dll", "build/.NETFramework/v4.6/System.ComponentModel.DataAnnotations.xml", "build/.NETFramework/v4.6/System.Configuration.Install.dll", "build/.NETFramework/v4.6/System.Configuration.Install.xml", "build/.NETFramework/v4.6/System.Configuration.dll", "build/.NETFramework/v4.6/System.Configuration.xml", "build/.NETFramework/v4.6/System.Core.dll", "build/.NETFramework/v4.6/System.Core.xml", "build/.NETFramework/v4.6/System.Data.DataSetExtensions.dll", "build/.NETFramework/v4.6/System.Data.DataSetExtensions.xml", "build/.NETFramework/v4.6/System.Data.Entity.Design.dll", "build/.NETFramework/v4.6/System.Data.Entity.Design.xml", "build/.NETFramework/v4.6/System.Data.Entity.dll", "build/.NETFramework/v4.6/System.Data.Entity.xml", "build/.NETFramework/v4.6/System.Data.Linq.dll", "build/.NETFramework/v4.6/System.Data.Linq.xml", "build/.NETFramework/v4.6/System.Data.OracleClient.dll", "build/.NETFramework/v4.6/System.Data.OracleClient.xml", "build/.NETFramework/v4.6/System.Data.Services.Client.dll", "build/.NETFramework/v4.6/System.Data.Services.Client.xml", "build/.NETFramework/v4.6/System.Data.Services.Design.dll", "build/.NETFramework/v4.6/System.Data.Services.Design.xml", "build/.NETFramework/v4.6/System.Data.Services.dll", "build/.NETFramework/v4.6/System.Data.Services.xml", "build/.NETFramework/v4.6/System.Data.SqlXml.dll", "build/.NETFramework/v4.6/System.Data.SqlXml.xml", "build/.NETFramework/v4.6/System.Data.dll", "build/.NETFramework/v4.6/System.Data.xml", "build/.NETFramework/v4.6/System.Deployment.dll", "build/.NETFramework/v4.6/System.Deployment.xml", "build/.NETFramework/v4.6/System.Design.dll", "build/.NETFramework/v4.6/System.Design.xml", "build/.NETFramework/v4.6/System.Device.dll", "build/.NETFramework/v4.6/System.Device.xml", "build/.NETFramework/v4.6/System.DirectoryServices.AccountManagement.dll", "build/.NETFramework/v4.6/System.DirectoryServices.AccountManagement.xml", "build/.NETFramework/v4.6/System.DirectoryServices.Protocols.dll", "build/.NETFramework/v4.6/System.DirectoryServices.Protocols.xml", "build/.NETFramework/v4.6/System.DirectoryServices.dll", "build/.NETFramework/v4.6/System.DirectoryServices.xml", "build/.NETFramework/v4.6/System.Drawing.Design.dll", "build/.NETFramework/v4.6/System.Drawing.Design.xml", "build/.NETFramework/v4.6/System.Drawing.dll", "build/.NETFramework/v4.6/System.Drawing.xml", "build/.NETFramework/v4.6/System.Dynamic.dll", "build/.NETFramework/v4.6/System.EnterpriseServices.Thunk.dll", "build/.NETFramework/v4.6/System.EnterpriseServices.Wrapper.dll", "build/.NETFramework/v4.6/System.EnterpriseServices.dll", "build/.NETFramework/v4.6/System.EnterpriseServices.xml", "build/.NETFramework/v4.6/System.IO.Compression.FileSystem.dll", "build/.NETFramework/v4.6/System.IO.Compression.FileSystem.xml", "build/.NETFramework/v4.6/System.IO.Compression.dll", "build/.NETFramework/v4.6/System.IO.Compression.xml", "build/.NETFramework/v4.6/System.IO.Log.dll", "build/.NETFramework/v4.6/System.IO.Log.xml", "build/.NETFramework/v4.6/System.IdentityModel.Selectors.dll", "build/.NETFramework/v4.6/System.IdentityModel.Selectors.xml", "build/.NETFramework/v4.6/System.IdentityModel.Services.dll", "build/.NETFramework/v4.6/System.IdentityModel.Services.xml", "build/.NETFramework/v4.6/System.IdentityModel.dll", "build/.NETFramework/v4.6/System.IdentityModel.xml", "build/.NETFramework/v4.6/System.Linq.xml", "build/.NETFramework/v4.6/System.Management.Instrumentation.dll", "build/.NETFramework/v4.6/System.Management.Instrumentation.xml", "build/.NETFramework/v4.6/System.Management.dll", "build/.NETFramework/v4.6/System.Management.xml", "build/.NETFramework/v4.6/System.Messaging.dll", "build/.NETFramework/v4.6/System.Messaging.xml", "build/.NETFramework/v4.6/System.Net.Http.WebRequest.dll", "build/.NETFramework/v4.6/System.Net.Http.WebRequest.xml", "build/.NETFramework/v4.6/System.Net.Http.dll", "build/.NETFramework/v4.6/System.Net.Http.xml", "build/.NETFramework/v4.6/System.Net.dll", "build/.NETFramework/v4.6/System.Net.xml", "build/.NETFramework/v4.6/System.Numerics.Vectors.dll", "build/.NETFramework/v4.6/System.Numerics.dll", "build/.NETFramework/v4.6/System.Numerics.xml", "build/.NETFramework/v4.6/System.Printing.dll", "build/.NETFramework/v4.6/System.Printing.xml", "build/.NETFramework/v4.6/System.Reflection.Context.dll", "build/.NETFramework/v4.6/System.Reflection.Context.xml", "build/.NETFramework/v4.6/System.Runtime.Caching.dll", "build/.NETFramework/v4.6/System.Runtime.Caching.xml", "build/.NETFramework/v4.6/System.Runtime.DurableInstancing.dll", "build/.NETFramework/v4.6/System.Runtime.DurableInstancing.xml", "build/.NETFramework/v4.6/System.Runtime.Remoting.dll", "build/.NETFramework/v4.6/System.Runtime.Remoting.xml", "build/.NETFramework/v4.6/System.Runtime.Serialization.Formatters.Soap.dll", "build/.NETFramework/v4.6/System.Runtime.Serialization.Formatters.Soap.xml", "build/.NETFramework/v4.6/System.Runtime.Serialization.dll", "build/.NETFramework/v4.6/System.Runtime.Serialization.xml", "build/.NETFramework/v4.6/System.Security.dll", "build/.NETFramework/v4.6/System.Security.xml", "build/.NETFramework/v4.6/System.ServiceModel.Activation.dll", "build/.NETFramework/v4.6/System.ServiceModel.Activation.xml", "build/.NETFramework/v4.6/System.ServiceModel.Activities.dll", "build/.NETFramework/v4.6/System.ServiceModel.Activities.xml", "build/.NETFramework/v4.6/System.ServiceModel.Channels.dll", "build/.NETFramework/v4.6/System.ServiceModel.Channels.xml", "build/.NETFramework/v4.6/System.ServiceModel.Discovery.dll", "build/.NETFramework/v4.6/System.ServiceModel.Discovery.xml", "build/.NETFramework/v4.6/System.ServiceModel.Routing.dll", "build/.NETFramework/v4.6/System.ServiceModel.Routing.xml", "build/.NETFramework/v4.6/System.ServiceModel.Web.dll", "build/.NETFramework/v4.6/System.ServiceModel.Web.xml", "build/.NETFramework/v4.6/System.ServiceModel.dll", "build/.NETFramework/v4.6/System.ServiceModel.xml", "build/.NETFramework/v4.6/System.ServiceProcess.dll", "build/.NETFramework/v4.6/System.ServiceProcess.xml", "build/.NETFramework/v4.6/System.Speech.dll", "build/.NETFramework/v4.6/System.Speech.xml", "build/.NETFramework/v4.6/System.Threading.Tasks.Dataflow.xml", "build/.NETFramework/v4.6/System.Transactions.dll", "build/.NETFramework/v4.6/System.Transactions.xml", "build/.NETFramework/v4.6/System.Web.Abstractions.dll", "build/.NETFramework/v4.6/System.Web.ApplicationServices.dll", "build/.NETFramework/v4.6/System.Web.ApplicationServices.xml", "build/.NETFramework/v4.6/System.Web.DataVisualization.Design.dll", "build/.NETFramework/v4.6/System.Web.DataVisualization.dll", "build/.NETFramework/v4.6/System.Web.DataVisualization.xml", "build/.NETFramework/v4.6/System.Web.DynamicData.Design.dll", "build/.NETFramework/v4.6/System.Web.DynamicData.Design.xml", "build/.NETFramework/v4.6/System.Web.DynamicData.dll", "build/.NETFramework/v4.6/System.Web.DynamicData.xml", "build/.NETFramework/v4.6/System.Web.Entity.Design.dll", "build/.NETFramework/v4.6/System.Web.Entity.Design.xml", "build/.NETFramework/v4.6/System.Web.Entity.dll", "build/.NETFramework/v4.6/System.Web.Entity.xml", "build/.NETFramework/v4.6/System.Web.Extensions.Design.dll", "build/.NETFramework/v4.6/System.Web.Extensions.Design.xml", "build/.NETFramework/v4.6/System.Web.Extensions.dll", "build/.NETFramework/v4.6/System.Web.Extensions.xml", "build/.NETFramework/v4.6/System.Web.Mobile.dll", "build/.NETFramework/v4.6/System.Web.Mobile.xml", "build/.NETFramework/v4.6/System.Web.RegularExpressions.dll", "build/.NETFramework/v4.6/System.Web.RegularExpressions.xml", "build/.NETFramework/v4.6/System.Web.Routing.dll", "build/.NETFramework/v4.6/System.Web.Services.dll", "build/.NETFramework/v4.6/System.Web.Services.xml", "build/.NETFramework/v4.6/System.Web.dll", "build/.NETFramework/v4.6/System.Web.xml", "build/.NETFramework/v4.6/System.Windows.Controls.Ribbon.dll", "build/.NETFramework/v4.6/System.Windows.Controls.Ribbon.xml", "build/.NETFramework/v4.6/System.Windows.Forms.DataVisualization.Design.dll", "build/.NETFramework/v4.6/System.Windows.Forms.DataVisualization.dll", "build/.NETFramework/v4.6/System.Windows.Forms.DataVisualization.xml", "build/.NETFramework/v4.6/System.Windows.Forms.dll", "build/.NETFramework/v4.6/System.Windows.Forms.xml", "build/.NETFramework/v4.6/System.Windows.Input.Manipulations.dll", "build/.NETFramework/v4.6/System.Windows.Input.Manipulations.xml", "build/.NETFramework/v4.6/System.Windows.Presentation.dll", "build/.NETFramework/v4.6/System.Windows.Presentation.xml", "build/.NETFramework/v4.6/System.Windows.dll", "build/.NETFramework/v4.6/System.Workflow.Activities.dll", "build/.NETFramework/v4.6/System.Workflow.Activities.xml", "build/.NETFramework/v4.6/System.Workflow.ComponentModel.dll", "build/.NETFramework/v4.6/System.Workflow.ComponentModel.xml", "build/.NETFramework/v4.6/System.Workflow.Runtime.dll", "build/.NETFramework/v4.6/System.Workflow.Runtime.xml", "build/.NETFramework/v4.6/System.WorkflowServices.dll", "build/.NETFramework/v4.6/System.WorkflowServices.xml", "build/.NETFramework/v4.6/System.Xaml.dll", "build/.NETFramework/v4.6/System.Xaml.xml", "build/.NETFramework/v4.6/System.Xml.Linq.dll", "build/.NETFramework/v4.6/System.Xml.Linq.xml", "build/.NETFramework/v4.6/System.Xml.Serialization.dll", "build/.NETFramework/v4.6/System.Xml.dll", "build/.NETFramework/v4.6/System.Xml.xml", "build/.NETFramework/v4.6/System.dll", "build/.NETFramework/v4.6/System.xml", "build/.NETFramework/v4.6/UIAutomationClient.dll", "build/.NETFramework/v4.6/UIAutomationClient.xml", "build/.NETFramework/v4.6/UIAutomationClientsideProviders.dll", "build/.NETFramework/v4.6/UIAutomationClientsideProviders.xml", "build/.NETFramework/v4.6/UIAutomationProvider.dll", "build/.NETFramework/v4.6/UIAutomationProvider.xml", "build/.NETFramework/v4.6/UIAutomationTypes.dll", "build/.NETFramework/v4.6/UIAutomationTypes.xml", "build/.NETFramework/v4.6/WindowsBase.dll", "build/.NETFramework/v4.6/WindowsBase.xml", "build/.NETFramework/v4.6/WindowsFormsIntegration.dll", "build/.NETFramework/v4.6/WindowsFormsIntegration.xml", "build/.NETFramework/v4.6/XamlBuildTask.dll", "build/.NETFramework/v4.6/XamlBuildTask.xml", "build/.NETFramework/v4.6/mscorlib.dll", "build/.NETFramework/v4.6/mscorlib.xml", "build/.NETFramework/v4.6/namespaces.xml", "build/.NETFramework/v4.6/sysglobl.dll", "build/.NETFramework/v4.6/sysglobl.xml", "build/Microsoft.NETFramework.ReferenceAssemblies.net46.targets", "microsoft.netframework.referenceassemblies.net46.1.0.2.nupkg.sha512", "microsoft.netframework.referenceassemblies.net46.nuspec"]}, "Mono.Cecil/0.11.4": {"sha512": "IC1h5g0NeJGHIUgzM1P82ld57knhP0IcQfrYITDPXlNpMYGUrsG5TxuaWTjaeqDNQMBDNZkB8L0rBnwsY6JHuQ==", "type": "package", "path": "mono.cecil/0.11.4", "files": [".nupkg.metadata", ".signature.p7s", "lib/net40/Mono.Cecil.Mdb.dll", "lib/net40/Mono.Cecil.Mdb.pdb", "lib/net40/Mono.Cecil.Pdb.dll", "lib/net40/Mono.Cecil.Pdb.pdb", "lib/net40/Mono.Cecil.Rocks.dll", "lib/net40/Mono.Cecil.Rocks.pdb", "lib/net40/Mono.Cecil.dll", "lib/net40/Mono.Cecil.pdb", "lib/netstandard2.0/Mono.Cecil.Mdb.dll", "lib/netstandard2.0/Mono.Cecil.Mdb.pdb", "lib/netstandard2.0/Mono.Cecil.Pdb.dll", "lib/netstandard2.0/Mono.Cecil.Pdb.pdb", "lib/netstandard2.0/Mono.Cecil.Rocks.dll", "lib/netstandard2.0/Mono.Cecil.Rocks.pdb", "lib/netstandard2.0/Mono.Cecil.dll", "lib/netstandard2.0/Mono.Cecil.pdb", "mono.cecil.0.11.4.nupkg.sha512", "mono.cecil.nuspec"]}, "MonoMod.RuntimeDetour/21.12.13.1": {"sha512": "65mB+bHjT6UCGCgO+/pYhpuGPf96rQ1Whwkut/x7cqVIW0DTndDFyWc/3bngzhnY/Y6IYtBMlXsU2ATq+CxpHg==", "type": "package", "path": "monomod.runtimedetour/21.12.13.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/net35/MonoMod.RuntimeDetour.dll", "lib/net35/MonoMod.RuntimeDetour.xml", "lib/net452/MonoMod.RuntimeDetour.dll", "lib/net452/MonoMod.RuntimeDetour.xml", "lib/net5.0/MonoMod.RuntimeDetour.dll", "lib/net5.0/MonoMod.RuntimeDetour.xml", "lib/netstandard2.0/MonoMod.RuntimeDetour.dll", "lib/netstandard2.0/MonoMod.RuntimeDetour.xml", "monomod.runtimedetour.21.12.13.1.nupkg.sha512", "monomod.runtimedetour.nuspec"]}, "MonoMod.Utils/21.12.13.1": {"sha512": "/H+0RMWqx/D1/fSuY5jhY6GFvqhdYdlnI7J3IfL8P6y4nJkoZDxqts6+RxWWOz4pbnJdWnxSjS8XG+Lq6rUi7w==", "type": "package", "path": "monomod.utils/21.12.13.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/net35/MonoMod.Utils.dll", "lib/net35/MonoMod.Utils.xml", "lib/net452/MonoMod.Utils.dll", "lib/net452/MonoMod.Utils.xml", "lib/net5.0/MonoMod.Utils.dll", "lib/net5.0/MonoMod.Utils.xml", "lib/netstandard2.0/MonoMod.Utils.dll", "lib/netstandard2.0/MonoMod.Utils.xml", "monomod.utils.21.12.13.1.nupkg.sha512", "monomod.utils.nuspec"]}, "UnityEngine.Modules/2018.4.11": {"sha512": "0oGA3MTVjYqM0ymyYot1oxi4cdVz+KN/bmNfx2U//fC3KRW7nU6Mwkb6gFF5WEHlk4USNPP8GmlqQDdNePv6vQ==", "type": "package", "path": "unityengine.modules/2018.4.11", "files": [".nupkg.metadata", "lib/net35/UnityEngine.AIModule.dll", "lib/net35/UnityEngine.ARModule.dll", "lib/net35/UnityEngine.AccessibilityModule.dll", "lib/net35/UnityEngine.AnimationModule.dll", "lib/net35/UnityEngine.AssetBundleModule.dll", "lib/net35/UnityEngine.AudioModule.dll", "lib/net35/UnityEngine.BaselibModule.dll", "lib/net35/UnityEngine.ClothModule.dll", "lib/net35/UnityEngine.ClusterInputModule.dll", "lib/net35/UnityEngine.ClusterRendererModule.dll", "lib/net35/UnityEngine.CoreModule.dll", "lib/net35/UnityEngine.CrashReportingModule.dll", "lib/net35/UnityEngine.DirectorModule.dll", "lib/net35/UnityEngine.FileSystemHttpModule.dll", "lib/net35/UnityEngine.GameCenterModule.dll", "lib/net35/UnityEngine.GridModule.dll", "lib/net35/UnityEngine.HotReloadModule.dll", "lib/net35/UnityEngine.IMGUIModule.dll", "lib/net35/UnityEngine.ImageConversionModule.dll", "lib/net35/UnityEngine.InputModule.dll", "lib/net35/UnityEngine.JSONSerializeModule.dll", "lib/net35/UnityEngine.LocalizationModule.dll", "lib/net35/UnityEngine.ParticleSystemModule.dll", "lib/net35/UnityEngine.PerformanceReportingModule.dll", "lib/net35/UnityEngine.Physics2DModule.dll", "lib/net35/UnityEngine.PhysicsModule.dll", "lib/net35/UnityEngine.ProfilerModule.dll", "lib/net35/UnityEngine.ScreenCaptureModule.dll", "lib/net35/UnityEngine.SharedInternalsModule.dll", "lib/net35/UnityEngine.SpriteMaskModule.dll", "lib/net35/UnityEngine.SpriteShapeModule.dll", "lib/net35/UnityEngine.StreamingModule.dll", "lib/net35/UnityEngine.StyleSheetsModule.dll", "lib/net35/UnityEngine.SubstanceModule.dll", "lib/net35/UnityEngine.TLSModule.dll", "lib/net35/UnityEngine.TerrainModule.dll", "lib/net35/UnityEngine.TerrainPhysicsModule.dll", "lib/net35/UnityEngine.TextCoreModule.dll", "lib/net35/UnityEngine.TextRenderingModule.dll", "lib/net35/UnityEngine.TilemapModule.dll", "lib/net35/UnityEngine.TimelineModule.dll", "lib/net35/UnityEngine.UIElementsModule.dll", "lib/net35/UnityEngine.UIModule.dll", "lib/net35/UnityEngine.UNETModule.dll", "lib/net35/UnityEngine.UmbraModule.dll", "lib/net35/UnityEngine.UnityAnalyticsModule.dll", "lib/net35/UnityEngine.UnityConnectModule.dll", "lib/net35/UnityEngine.UnityTestProtocolModule.dll", "lib/net35/UnityEngine.UnityWebRequestAssetBundleModule.dll", "lib/net35/UnityEngine.UnityWebRequestAudioModule.dll", "lib/net35/UnityEngine.UnityWebRequestModule.dll", "lib/net35/UnityEngine.UnityWebRequestTextureModule.dll", "lib/net35/UnityEngine.UnityWebRequestWWWModule.dll", "lib/net35/UnityEngine.VFXModule.dll", "lib/net35/UnityEngine.VRModule.dll", "lib/net35/UnityEngine.VehiclesModule.dll", "lib/net35/UnityEngine.VideoModule.dll", "lib/net35/UnityEngine.WindModule.dll", "lib/net35/UnityEngine.XRModule.dll", "lib/net35/UnityEngine.dll", "lib/net45/UnityEngine.AIModule.dll", "lib/net45/UnityEngine.ARModule.dll", "lib/net45/UnityEngine.AccessibilityModule.dll", "lib/net45/UnityEngine.AnimationModule.dll", "lib/net45/UnityEngine.AssetBundleModule.dll", "lib/net45/UnityEngine.AudioModule.dll", "lib/net45/UnityEngine.BaselibModule.dll", "lib/net45/UnityEngine.ClothModule.dll", "lib/net45/UnityEngine.ClusterInputModule.dll", "lib/net45/UnityEngine.ClusterRendererModule.dll", "lib/net45/UnityEngine.CoreModule.dll", "lib/net45/UnityEngine.CrashReportingModule.dll", "lib/net45/UnityEngine.DirectorModule.dll", "lib/net45/UnityEngine.FileSystemHttpModule.dll", "lib/net45/UnityEngine.GameCenterModule.dll", "lib/net45/UnityEngine.GridModule.dll", "lib/net45/UnityEngine.HotReloadModule.dll", "lib/net45/UnityEngine.IMGUIModule.dll", "lib/net45/UnityEngine.ImageConversionModule.dll", "lib/net45/UnityEngine.InputModule.dll", "lib/net45/UnityEngine.JSONSerializeModule.dll", "lib/net45/UnityEngine.LocalizationModule.dll", "lib/net45/UnityEngine.ParticleSystemModule.dll", "lib/net45/UnityEngine.PerformanceReportingModule.dll", "lib/net45/UnityEngine.Physics2DModule.dll", "lib/net45/UnityEngine.PhysicsModule.dll", "lib/net45/UnityEngine.ProfilerModule.dll", "lib/net45/UnityEngine.ScreenCaptureModule.dll", "lib/net45/UnityEngine.SharedInternalsModule.dll", "lib/net45/UnityEngine.SpriteMaskModule.dll", "lib/net45/UnityEngine.SpriteShapeModule.dll", "lib/net45/UnityEngine.StreamingModule.dll", "lib/net45/UnityEngine.StyleSheetsModule.dll", "lib/net45/UnityEngine.SubstanceModule.dll", "lib/net45/UnityEngine.TLSModule.dll", "lib/net45/UnityEngine.TerrainModule.dll", "lib/net45/UnityEngine.TerrainPhysicsModule.dll", "lib/net45/UnityEngine.TextCoreModule.dll", "lib/net45/UnityEngine.TextRenderingModule.dll", "lib/net45/UnityEngine.TilemapModule.dll", "lib/net45/UnityEngine.TimelineModule.dll", "lib/net45/UnityEngine.UIElementsModule.dll", "lib/net45/UnityEngine.UIModule.dll", "lib/net45/UnityEngine.UNETModule.dll", "lib/net45/UnityEngine.UmbraModule.dll", "lib/net45/UnityEngine.UnityAnalyticsModule.dll", "lib/net45/UnityEngine.UnityConnectModule.dll", "lib/net45/UnityEngine.UnityTestProtocolModule.dll", "lib/net45/UnityEngine.UnityWebRequestAssetBundleModule.dll", "lib/net45/UnityEngine.UnityWebRequestAudioModule.dll", "lib/net45/UnityEngine.UnityWebRequestModule.dll", "lib/net45/UnityEngine.UnityWebRequestTextureModule.dll", "lib/net45/UnityEngine.UnityWebRequestWWWModule.dll", "lib/net45/UnityEngine.VFXModule.dll", "lib/net45/UnityEngine.VRModule.dll", "lib/net45/UnityEngine.VehiclesModule.dll", "lib/net45/UnityEngine.VideoModule.dll", "lib/net45/UnityEngine.WindModule.dll", "lib/net45/UnityEngine.XRModule.dll", "lib/net45/UnityEngine.dll", "lib/netstandard2.0/UnityEngine.AIModule.dll", "lib/netstandard2.0/UnityEngine.ARModule.dll", "lib/netstandard2.0/UnityEngine.AccessibilityModule.dll", "lib/netstandard2.0/UnityEngine.AnimationModule.dll", "lib/netstandard2.0/UnityEngine.AssetBundleModule.dll", "lib/netstandard2.0/UnityEngine.AudioModule.dll", "lib/netstandard2.0/UnityEngine.BaselibModule.dll", "lib/netstandard2.0/UnityEngine.ClothModule.dll", "lib/netstandard2.0/UnityEngine.ClusterInputModule.dll", "lib/netstandard2.0/UnityEngine.ClusterRendererModule.dll", "lib/netstandard2.0/UnityEngine.CoreModule.dll", "lib/netstandard2.0/UnityEngine.CrashReportingModule.dll", "lib/netstandard2.0/UnityEngine.DirectorModule.dll", "lib/netstandard2.0/UnityEngine.FileSystemHttpModule.dll", "lib/netstandard2.0/UnityEngine.GameCenterModule.dll", "lib/netstandard2.0/UnityEngine.GridModule.dll", "lib/netstandard2.0/UnityEngine.HotReloadModule.dll", "lib/netstandard2.0/UnityEngine.IMGUIModule.dll", "lib/netstandard2.0/UnityEngine.ImageConversionModule.dll", "lib/netstandard2.0/UnityEngine.InputModule.dll", "lib/netstandard2.0/UnityEngine.JSONSerializeModule.dll", "lib/netstandard2.0/UnityEngine.LocalizationModule.dll", "lib/netstandard2.0/UnityEngine.ParticleSystemModule.dll", "lib/netstandard2.0/UnityEngine.PerformanceReportingModule.dll", "lib/netstandard2.0/UnityEngine.Physics2DModule.dll", "lib/netstandard2.0/UnityEngine.PhysicsModule.dll", "lib/netstandard2.0/UnityEngine.ProfilerModule.dll", "lib/netstandard2.0/UnityEngine.ScreenCaptureModule.dll", "lib/netstandard2.0/UnityEngine.SharedInternalsModule.dll", "lib/netstandard2.0/UnityEngine.SpriteMaskModule.dll", "lib/netstandard2.0/UnityEngine.SpriteShapeModule.dll", "lib/netstandard2.0/UnityEngine.StreamingModule.dll", "lib/netstandard2.0/UnityEngine.StyleSheetsModule.dll", "lib/netstandard2.0/UnityEngine.SubstanceModule.dll", "lib/netstandard2.0/UnityEngine.TLSModule.dll", "lib/netstandard2.0/UnityEngine.TerrainModule.dll", "lib/netstandard2.0/UnityEngine.TerrainPhysicsModule.dll", "lib/netstandard2.0/UnityEngine.TextCoreModule.dll", "lib/netstandard2.0/UnityEngine.TextRenderingModule.dll", "lib/netstandard2.0/UnityEngine.TilemapModule.dll", "lib/netstandard2.0/UnityEngine.TimelineModule.dll", "lib/netstandard2.0/UnityEngine.UIElementsModule.dll", "lib/netstandard2.0/UnityEngine.UIModule.dll", "lib/netstandard2.0/UnityEngine.UNETModule.dll", "lib/netstandard2.0/UnityEngine.UmbraModule.dll", "lib/netstandard2.0/UnityEngine.UnityAnalyticsModule.dll", "lib/netstandard2.0/UnityEngine.UnityConnectModule.dll", "lib/netstandard2.0/UnityEngine.UnityTestProtocolModule.dll", "lib/netstandard2.0/UnityEngine.UnityWebRequestAssetBundleModule.dll", "lib/netstandard2.0/UnityEngine.UnityWebRequestAudioModule.dll", "lib/netstandard2.0/UnityEngine.UnityWebRequestModule.dll", "lib/netstandard2.0/UnityEngine.UnityWebRequestTextureModule.dll", "lib/netstandard2.0/UnityEngine.UnityWebRequestWWWModule.dll", "lib/netstandard2.0/UnityEngine.VFXModule.dll", "lib/netstandard2.0/UnityEngine.VRModule.dll", "lib/netstandard2.0/UnityEngine.VehiclesModule.dll", "lib/netstandard2.0/UnityEngine.VideoModule.dll", "lib/netstandard2.0/UnityEngine.WindModule.dll", "lib/netstandard2.0/UnityEngine.XRModule.dll", "lib/netstandard2.0/UnityEngine.dll", "unityengine.modules.2018.4.11.nupkg.sha512", "unityengine.modules.nuspec"]}}, "projectFileDependencyGroups": {".NETFramework,Version=v4.6": ["BepInEx.Analyzers >= 1.*", "BepInEx.Core >= 5.*", "BepInEx.PluginInfoProps >= 2.*", "Microsoft.NETFramework.ReferenceAssemblies >= 1.0.2", "UnityEngine.Modules >= 2018.4.11"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\Plugins DEV\\MyThirdPluginCollision\\MyThirdPluginCollision.csproj", "projectName": "MyThirdPluginCollision", "projectPath": "E:\\Plugins DEV\\MyThirdPluginCollision\\MyThirdPluginCollision.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\Plugins DEV\\MyThirdPluginCollision\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net46"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.bepinex.dev/v3/index.json": {}, "https://nuget.samboy.dev/v3/index.json": {}}, "frameworks": {"net46": {"targetAlias": "net46", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net46": {"targetAlias": "net46", "dependencies": {"BepInEx.Analyzers": {"suppressParent": "All", "target": "Package", "version": "[1.*, )"}, "BepInEx.Core": {"target": "Package", "version": "[5.*, )"}, "BepInEx.PluginInfoProps": {"target": "Package", "version": "[2.*, )"}, "Microsoft.NETFramework.ReferenceAssemblies": {"suppressParent": "All", "target": "Package", "version": "[1.0.2, )"}, "UnityEngine.Modules": {"include": "Compile", "target": "Package", "version": "[2018.4.11, )"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.401\\RuntimeIdentifierGraph.json"}}}}