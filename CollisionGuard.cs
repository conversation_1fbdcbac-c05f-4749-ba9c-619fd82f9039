
using BepInEx;
using UnityEngine;
using Studio;
using AIChara;
using System.Collections.Generic;
using System.Linq;
using System;
using System.Collections;
using System.Reflection;

namespace StudioPowerTools
{
    [BepInPlugin("com.example.collisionguard", "Advanced Collision Guard", "2.0.0")]
    public class CollisionGuard : BaseUnityPlugin
    {
        #region Plugin State
        private bool isVisible = false;
        private Rect windowRect = new Rect(Screen.width - 450, 20, 430, 700);
        private bool isInitialized = false;
        #endregion

        #region Monitored Objects
        private OCIChar targetBodyChar;
        private OCIChar clippingItemChar;
        private List<DynamicBoneCollider> targetColliders = new List<DynamicBoneCollider>();
        private List<object> clippingParticles = new List<object>(); // Using object to avoid private type access
        private List<DynamicBone> dynamicBones = new List<DynamicBone>();
        #endregion

        #region Advanced Collision Detection
        private Dictionary<Transform, CollisionInfo> activeCollisions = new Dictionary<Transform, CollisionInfo>();
        private Dictionary<Transform, CollisionInfo> predictedCollisions = new Dictionary<Transform, CollisionInfo>();
        private SpatialGrid spatialGrid;
        private float detectionRadius = 0.1f;
        private bool enablePredictiveDetection = true;
        private float predictionTime = 0.1f;
        #endregion

        #region Anti-Clipping Strategies
        public enum AntiClippingMode
        {
            Push,           // 推力模式
            Pull,           // 拉力模式
            Constraint,     // 约束模式
            Hybrid,         // 混合模式
            Physics         // 物理模式
        }

        private AntiClippingMode currentMode = AntiClippingMode.Hybrid;
        private bool autoCorrectionEnabled = true;
        private float pushForce = 0.02f;
        private float pullForce = 0.015f;
        private float constraintStiffness = 0.8f;
        private float dampingFactor = 0.95f;
        private bool enablePhysicsConstraints = true;
        #endregion

        #region UI & Correction State
        private Vector2 scrollPosition = Vector2.zero;
        private Transform selectedBone = null;
        private Vector3 manualOffset = Vector3.zero;
        private Dictionary<Transform, Vector3> originalLocalPositions = new Dictionary<Transform, Vector3>();
        private Dictionary<Transform, Vector3> boneVelocities = new Dictionary<Transform, Vector3>();
        private int selectedTab = 0;
        private string[] tabNames = { "检测", "策略", "约束", "调试" };
        #endregion

        #region Visuals & Debug
        private List<GameObject> collisionMarkers = new List<GameObject>();
        private List<GameObject> predictionMarkers = new List<GameObject>();
        private GameObject selectionMarker;
        private bool showCollisionMarkers = true;
        private bool showPredictionMarkers = true;
        private bool showDebugInfo = false;
        private Material markerMaterial;
        #endregion

        #region Physics Constraints
        private List<DistanceConstraint> distanceConstraints = new List<DistanceConstraint>();
        private List<SpringConstraint> springConstraints = new List<SpringConstraint>();
        private bool enableDistanceConstraints = true;
        private bool enableSpringConstraints = true;
        private float maxConstraintDistance = 0.05f;
        #endregion

        #region Reflection Helpers
        private static FieldInfo particlesField;
        private static FieldInfo transformField;
        private static FieldInfo radiusField;

        static CollisionGuard()
        {
            // Cache reflection info for better performance
            particlesField = typeof(DynamicBone).GetField("m_Particles",
                BindingFlags.NonPublic | BindingFlags.Instance);
            transformField = typeof(DynamicBone).GetNestedType("Particle",
                BindingFlags.NonPublic)?.GetField("m_Transform",
                BindingFlags.NonPublic | BindingFlags.Instance);
            radiusField = typeof(DynamicBone).GetNestedType("Particle",
                BindingFlags.NonPublic)?.GetField("m_Radius",
                BindingFlags.NonPublic | BindingFlags.Instance);
        }

        private Transform GetParticleTransform(object particle)
        {
            return transformField?.GetValue(particle) as Transform;
        }

        private float GetParticleRadius(object particle)
        {
            var radius = radiusField?.GetValue(particle);
            return radius != null ? (float)radius : 0.01f;
        }
        #endregion

        #region Data Structures
        public class CollisionInfo
        {
            public Transform bone;
            public DynamicBoneCollider collider;
            public Vector3 penetrationVector;
            public float penetrationDepth;
            public Vector3 contactPoint;
            public Vector3 contactNormal;
            public float timestamp;
            public CollisionType type;
        }

        public enum CollisionType
        {
            Sphere,
            Capsule,
            Box,
            Plane
        }

        public class DistanceConstraint
        {
            public Transform boneA;
            public Transform boneB;
            public float restDistance;
            public float stiffness;
            public bool isActive;
        }

        public class SpringConstraint
        {
            public Transform bone;
            public Vector3 anchorPoint;
            public float springConstant;
            public float damping;
            public float restLength;
            public bool isActive;
        }

        public class SpatialGrid
        {
            private Dictionary<Vector3Int, List<Transform>> grid;
            private float cellSize;

            public SpatialGrid(float cellSize = 0.1f)
            {
                this.cellSize = cellSize;
                this.grid = new Dictionary<Vector3Int, List<Transform>>();
            }

            public Vector3Int GetCellKey(Vector3 position)
            {
                return new Vector3Int(
                    Mathf.FloorToInt(position.x / cellSize),
                    Mathf.FloorToInt(position.y / cellSize),
                    Mathf.FloorToInt(position.z / cellSize)
                );
            }

            public void Clear()
            {
                foreach (var cell in grid.Values)
                    cell.Clear();
            }

            public void AddBone(Transform bone)
            {
                var key = GetCellKey(bone.position);
                if (!grid.ContainsKey(key))
                    grid[key] = new List<Transform>();
                grid[key].Add(bone);
            }

            public List<Transform> GetNearbyBones(Vector3 position, float radius)
            {
                var result = new List<Transform>();
                var center = GetCellKey(position);
                int range = Mathf.CeilToInt(radius / cellSize);

                for (int x = -range; x <= range; x++)
                {
                    for (int y = -range; y <= range; y++)
                    {
                        for (int z = -range; z <= range; z++)
                        {
                            var key = center + new Vector3Int(x, y, z);
                            if (grid.ContainsKey(key))
                                result.AddRange(grid[key]);
                        }
                    }
                }
                return result;
            }
        }
        #endregion

        void Awake()
        {
            spatialGrid = new SpatialGrid(0.1f);
            InitializeMaterials();
        }

        void Update()
        {
            if (Input.GetKeyDown(KeyCode.F4))
            {
                isVisible = !isVisible;
                // 可视化清理已移除
            }

            if (!isInitialized) return;

            if (targetBodyChar != null && clippingItemChar != null)
            {
                // Update spatial grid
                UpdateSpatialGrid();

                // Detect current collisions
                DetectCollisions();

                // Predict future collisions if enabled
                if (enablePredictiveDetection)
                    PredictCollisions();

                // Apply anti-clipping corrections
                if (autoCorrectionEnabled)
                    ApplyAntiClippingCorrections();

                // Update physics constraints
                if (enablePhysicsConstraints)
                    UpdatePhysicsConstraints();

                // 可视化更新已移除
            }
        }

        void InitializeMaterials()
        {
            markerMaterial = new Material(Shader.Find("Standard"));
            markerMaterial.SetInt("_SrcBlend", (int)UnityEngine.Rendering.BlendMode.SrcAlpha);
            markerMaterial.SetInt("_DstBlend", (int)UnityEngine.Rendering.BlendMode.OneMinusSrcAlpha);
            markerMaterial.SetInt("_ZWrite", 0);
            markerMaterial.EnableKeyword("_ALPHABLEND_ON");
            markerMaterial.renderQueue = 3000;
        }

        void UpdateSpatialGrid()
        {
            spatialGrid.Clear();
            foreach (var particle in clippingParticles)
            {
                var transform = GetParticleTransform(particle);
                if (transform != null)
                    spatialGrid.AddBone(transform);
            }
        }

        void DetectCollisions()
        {
            activeCollisions.Clear();

            foreach (var particle in clippingParticles)
            {
                var transform = GetParticleTransform(particle);
                if (transform == null) continue;

                var nearbyBones = spatialGrid.GetNearbyBones(transform.position, detectionRadius);
                CollisionInfo deepestCollision = null;
                float maxDepth = 0f;

                foreach (var collider in targetColliders)
                {
                    if (collider == null) continue;

                    var collisionInfo = CheckCollision(particle, collider);
                    if (collisionInfo != null && collisionInfo.penetrationDepth > maxDepth)
                    {
                        deepestCollision = collisionInfo;
                        maxDepth = collisionInfo.penetrationDepth;
                    }
                }

                if (deepestCollision != null)
                {
                    activeCollisions[transform] = deepestCollision;
                }
            }
        }

        CollisionInfo CheckCollision(object particle, DynamicBoneCollider collider)
        {
            var transform = GetParticleTransform(particle);
            if (transform == null) return null;

            Vector3 particlePos = transform.position;
            float particleRadius = GetParticleRadius(particle);
            Vector3 colliderCenter = collider.transform.TransformPoint(collider.m_Center);

            CollisionInfo info = null;

            switch (collider.m_Direction)
            {
                case DynamicBoneColliderBase.Direction.X:
                case DynamicBoneColliderBase.Direction.Y:
                case DynamicBoneColliderBase.Direction.Z:
                    // Capsule collision
                    info = CheckCapsuleCollision(particlePos, particleRadius, collider);
                    break;
                default:
                    // Sphere collision
                    info = CheckSphereCollision(particlePos, particleRadius, collider);
                    break;
            }

            if (info != null)
            {
                info.bone = transform;
                info.collider = collider;
                info.timestamp = Time.time;
            }

            return info;
        }

        CollisionInfo CheckSphereCollision(Vector3 particlePos, float particleRadius, DynamicBoneCollider collider)
        {
            Vector3 colliderCenter = collider.transform.TransformPoint(collider.m_Center);
            float distance = Vector3.Distance(particlePos, colliderCenter);
            float totalRadius = particleRadius + collider.m_Radius;

            if (distance < totalRadius)
            {
                Vector3 direction = (particlePos - colliderCenter).normalized;
                float penetrationDepth = totalRadius - distance;

                return new CollisionInfo
                {
                    penetrationVector = direction * penetrationDepth,
                    penetrationDepth = penetrationDepth,
                    contactPoint = colliderCenter + direction * collider.m_Radius,
                    contactNormal = direction,
                    type = CollisionType.Sphere
                };
            }

            return null;
        }

        CollisionInfo CheckCapsuleCollision(Vector3 particlePos, float particleRadius, DynamicBoneCollider collider)
        {
            // Simplified capsule collision - can be enhanced further
            Vector3 colliderCenter = collider.transform.TransformPoint(collider.m_Center);
            Vector3 axis = GetColliderAxis(collider);
            float halfHeight = collider.m_Height * 0.5f;

            Vector3 p1 = colliderCenter - axis * halfHeight;
            Vector3 p2 = colliderCenter + axis * halfHeight;

            Vector3 closestPoint = ClosestPointOnLineSegment(particlePos, p1, p2);
            float distance = Vector3.Distance(particlePos, closestPoint);
            float totalRadius = particleRadius + collider.m_Radius;

            if (distance < totalRadius)
            {
                Vector3 direction = (particlePos - closestPoint).normalized;
                float penetrationDepth = totalRadius - distance;

                return new CollisionInfo
                {
                    penetrationVector = direction * penetrationDepth,
                    penetrationDepth = penetrationDepth,
                    contactPoint = closestPoint + direction * collider.m_Radius,
                    contactNormal = direction,
                    type = CollisionType.Capsule
                };
            }

            return null;
        }

        Vector3 GetColliderAxis(DynamicBoneCollider collider)
        {
            switch (collider.m_Direction)
            {
                case DynamicBoneColliderBase.Direction.X:
                    return collider.transform.right;
                case DynamicBoneColliderBase.Direction.Y:
                    return collider.transform.up;
                case DynamicBoneColliderBase.Direction.Z:
                    return collider.transform.forward;
                default:
                    return Vector3.up;
            }
        }

        Vector3 ClosestPointOnLineSegment(Vector3 point, Vector3 a, Vector3 b)
        {
            Vector3 ab = b - a;
            float t = Vector3.Dot(point - a, ab) / Vector3.Dot(ab, ab);
            t = Mathf.Clamp01(t);
            return a + t * ab;
        }

        void PredictCollisions()
        {
            predictedCollisions.Clear();

            foreach (var particle in clippingParticles)
            {
                var transform = GetParticleTransform(particle);
                if (transform == null) continue;

                // Calculate predicted position based on velocity
                Vector3 currentPos = transform.position;
                Vector3 velocity = GetBoneVelocity(transform);
                Vector3 predictedPos = currentPos + velocity * predictionTime;

                foreach (var collider in targetColliders)
                {
                    if (collider == null) continue;

                    var collisionInfo = CheckPredictedCollision(particle, collider, predictedPos);
                    if (collisionInfo != null)
                    {
                        predictedCollisions[transform] = collisionInfo;
                        break;
                    }
                }
            }
        }

        Vector3 GetBoneVelocity(Transform bone)
        {
            if (!boneVelocities.ContainsKey(bone))
            {
                boneVelocities[bone] = Vector3.zero;
                return Vector3.zero;
            }

            Vector3 currentPos = bone.position;
            Vector3 lastPos = currentPos - boneVelocities[bone] * Time.deltaTime;
            Vector3 velocity = (currentPos - lastPos) / Time.deltaTime;

            boneVelocities[bone] = velocity;
            return velocity;
        }

        CollisionInfo CheckPredictedCollision(object particle, DynamicBoneCollider collider, Vector3 predictedPos)
        {
            // Similar to CheckCollision but using predicted position
            CollisionInfo info = null;
            float particleRadius = GetParticleRadius(particle);
            Transform particleTransform = GetParticleTransform(particle);

            switch (collider.m_Direction)
            {
                case DynamicBoneColliderBase.Direction.X:
                case DynamicBoneColliderBase.Direction.Y:
                case DynamicBoneColliderBase.Direction.Z:
                    info = CheckPredictedCapsuleCollision(predictedPos, particleRadius, collider);
                    break;
                default:
                    info = CheckPredictedSphereCollision(predictedPos, particleRadius, collider);
                    break;
            }

            if (info != null && particleTransform != null)
            {
                info.bone = particleTransform;
                info.collider = collider;
                info.timestamp = Time.time + predictionTime;
            }

            return info;
        }

        CollisionInfo CheckPredictedSphereCollision(Vector3 predictedPos, float particleRadius, DynamicBoneCollider collider)
        {
            Vector3 colliderCenter = collider.transform.TransformPoint(collider.m_Center);
            float distance = Vector3.Distance(predictedPos, colliderCenter);
            float totalRadius = particleRadius + collider.m_Radius;

            if (distance < totalRadius)
            {
                Vector3 direction = (predictedPos - colliderCenter).normalized;
                float penetrationDepth = totalRadius - distance;

                return new CollisionInfo
                {
                    penetrationVector = direction * penetrationDepth,
                    penetrationDepth = penetrationDepth,
                    contactPoint = colliderCenter + direction * collider.m_Radius,
                    contactNormal = direction,
                    type = CollisionType.Sphere
                };
            }

            return null;
        }

        CollisionInfo CheckPredictedCapsuleCollision(Vector3 predictedPos, float particleRadius, DynamicBoneCollider collider)
        {
            Vector3 colliderCenter = collider.transform.TransformPoint(collider.m_Center);
            Vector3 axis = GetColliderAxis(collider);
            float halfHeight = collider.m_Height * 0.5f;

            Vector3 p1 = colliderCenter - axis * halfHeight;
            Vector3 p2 = colliderCenter + axis * halfHeight;

            Vector3 closestPoint = ClosestPointOnLineSegment(predictedPos, p1, p2);
            float distance = Vector3.Distance(predictedPos, closestPoint);
            float totalRadius = particleRadius + collider.m_Radius;

            if (distance < totalRadius)
            {
                Vector3 direction = (predictedPos - closestPoint).normalized;
                float penetrationDepth = totalRadius - distance;

                return new CollisionInfo
                {
                    penetrationVector = direction * penetrationDepth,
                    penetrationDepth = penetrationDepth,
                    contactPoint = closestPoint + direction * collider.m_Radius,
                    contactNormal = direction,
                    type = CollisionType.Capsule
                };
            }

            return null;
        }

        void ApplyAntiClippingCorrections()
        {
            switch (currentMode)
            {
                case AntiClippingMode.Push:
                    ApplyPushCorrection();
                    break;
                case AntiClippingMode.Pull:
                    ApplyPullCorrection();
                    break;
                case AntiClippingMode.Constraint:
                    ApplyConstraintCorrection();
                    break;
                case AntiClippingMode.Hybrid:
                    ApplyHybridCorrection();
                    break;
                case AntiClippingMode.Physics:
                    ApplyPhysicsCorrection();
                    break;
            }
        }

        void ApplyPushCorrection()
        {
            foreach (var collision in activeCollisions.Values)
            {
                Vector3 correction = collision.contactNormal * collision.penetrationDepth * pushForce;
                collision.bone.position += correction * Time.deltaTime;

                // Apply damping
                if (boneVelocities.ContainsKey(collision.bone))
                    boneVelocities[collision.bone] *= dampingFactor;
            }
        }

        void ApplyPullCorrection()
        {
            foreach (var collision in activeCollisions.Values)
            {
                Vector3 targetPos = collision.contactPoint + collision.contactNormal * (collision.penetrationDepth + 0.01f);
                Vector3 correction = (targetPos - collision.bone.position) * pullForce;
                collision.bone.position += correction * Time.deltaTime;
            }
        }

        void ApplyConstraintCorrection()
        {
            foreach (var collision in activeCollisions.Values)
            {
                Vector3 targetPos = collision.contactPoint + collision.contactNormal * 0.01f;
                Vector3 correction = (targetPos - collision.bone.position) * constraintStiffness;
                collision.bone.position += correction;
            }
        }

        void ApplyHybridCorrection()
        {
            foreach (var collision in activeCollisions.Values)
            {
                float severity = collision.penetrationDepth / 0.05f; // Normalize severity
                severity = Mathf.Clamp01(severity);

                if (severity < 0.3f)
                {
                    // Light collision - use push
                    Vector3 correction = collision.contactNormal * collision.penetrationDepth * pushForce;
                    collision.bone.position += correction * Time.deltaTime;
                }
                else if (severity < 0.7f)
                {
                    // Medium collision - blend push and pull
                    Vector3 pushCorrection = collision.contactNormal * collision.penetrationDepth * pushForce * 0.5f;
                    Vector3 targetPos = collision.contactPoint + collision.contactNormal * 0.01f;
                    Vector3 pullCorrection = (targetPos - collision.bone.position) * pullForce * 0.7f;
                    
                    collision.bone.position += (pushCorrection + pullCorrection) * Time.deltaTime;
                }
                else
                {
                    // Severe collision - use constraint
                    Vector3 targetPos = collision.contactPoint + collision.contactNormal * 0.01f;
                    Vector3 correction = (targetPos - collision.bone.position) * constraintStiffness;
                    collision.bone.position += correction;
                }
            }
        }

        void ApplyPhysicsCorrection()
        {
            foreach (var collision in activeCollisions.Values)
            {
                // Apply impulse-based correction
                Vector3 impulse = collision.contactNormal * collision.penetrationDepth * pushForce * 10f;

                if (!boneVelocities.ContainsKey(collision.bone))
                    boneVelocities[collision.bone] = Vector3.zero;

                boneVelocities[collision.bone] += impulse;
                collision.bone.position += boneVelocities[collision.bone] * Time.deltaTime;

                // Apply damping
                boneVelocities[collision.bone] *= dampingFactor;
            }
        }

        void UpdatePhysicsConstraints()
        {
            if (enableDistanceConstraints)
                UpdateDistanceConstraints();

            if (enableSpringConstraints)
                UpdateSpringConstraints();
        }

        void UpdateDistanceConstraints()
        {
            for (int i = distanceConstraints.Count - 1; i >= 0; i--)
            {
                var constraint = distanceConstraints[i];
                if (constraint.boneA == null || constraint.boneB == null)
                {
                    distanceConstraints.RemoveAt(i);
                    continue;
                }
                
                if (!constraint.isActive) continue;

                Vector3 delta = constraint.boneB.position - constraint.boneA.position;
                float currentDistance = delta.magnitude;
                float difference = currentDistance - constraint.restDistance;

                if (Mathf.Abs(difference) > 0.001f)
                {
                    Vector3 correction = delta.normalized * difference * constraint.stiffness * 0.5f;
                    
                    // Apply correction only if it doesn't cause new collisions
                    if (!WouldCauseCollision(constraint.boneA, correction) && 
                        !WouldCauseCollision(constraint.boneB, -correction))
                    {
                        constraint.boneA.position += correction;
                        constraint.boneB.position -= correction;
                    }
                }
            }
        }
        
        bool WouldCauseCollision(Transform bone, Vector3 displacement)
        {
            Vector3 newPos = bone.position + displacement;
            foreach (var collider in targetColliders)
            {
                if (collider == null) continue;
                
                // Simplified collision check
                float distance = Vector3.Distance(newPos, collider.transform.TransformPoint(collider.m_Center));
                if (distance < collider.m_Radius + 0.01f) 
                    return true;
            }
            return false;
        }

        void UpdateSpringConstraints()
        {
            foreach (var constraint in springConstraints)
            {
                if (!constraint.isActive || constraint.bone == null)
                    continue;

                Vector3 delta = constraint.bone.position - constraint.anchorPoint;
                float currentDistance = delta.magnitude;
                float springForce = (currentDistance - constraint.restLength) * constraint.springConstant;

                Vector3 force = -delta.normalized * springForce;

                if (!boneVelocities.ContainsKey(constraint.bone))
                    boneVelocities[constraint.bone] = Vector3.zero;

                boneVelocities[constraint.bone] += force * Time.deltaTime;
                boneVelocities[constraint.bone] *= (1f - constraint.damping * Time.deltaTime);

                constraint.bone.position += boneVelocities[constraint.bone] * Time.deltaTime;
            }
        }

        void OnGUI()
        {
            if (!isVisible) return;
            windowRect = GUI.Window(1, windowRect, DrawWindow, "Advanced Collision Guard v2.0");
        }

        void DrawWindow(int windowID)
        {
            GUI.DragWindow(new Rect(0, 0, 10000, 20));

            // Tab system
            selectedTab = GUI.Toolbar(new Rect(10, 25, 410, 25), selectedTab, tabNames);

            switch (selectedTab)
            {
                case 0: DrawDetectionTab(); break;
                case 1: DrawStrategyTab(); break;
                case 2: DrawConstraintTab(); break;
                case 3: DrawDebugTab(); break;
            }
        }

        void DrawDetectionTab()
        {
            // --- Object Selection ---
            GUI.Box(new Rect(10, 60, 410, 110), "对象选择");
            GUI.Label(new Rect(20, 85, 390, 20), $"目标身体: {(targetBodyChar != null ? targetBodyChar.charInfo.chaFile.parameter.fullname : "未选择")}");
            GUI.Label(new Rect(20, 110, 390, 20), $"穿模物体: {(clippingItemChar != null ? clippingItemChar.charInfo.chaFile.parameter.fullname : "未选择")}");
            if (GUI.Button(new Rect(20, 135, 190, 25), "设置选中为目标")) SetObject(true);
            if (GUI.Button(new Rect(220, 135, 190, 25), "设置选中为物体")) SetObject(false);

            // --- Detection Settings ---
            GUI.Box(new Rect(10, 180, 410, 120), "检测设置");
            GUI.Label(new Rect(20, 205, 100, 20), "检测半径:");
            detectionRadius = GUI.HorizontalSlider(new Rect(120, 210, 200, 20), detectionRadius, 0.01f, 0.2f);
            GUI.Label(new Rect(330, 205, 80, 20), detectionRadius.ToString("F3"));

            enablePredictiveDetection = GUI.Toggle(new Rect(20, 230, 200, 20), enablePredictiveDetection, "启用预测性检测");
            if (enablePredictiveDetection)
            {
                GUI.Label(new Rect(40, 255, 100, 20), "预测时间:");
                predictionTime = GUI.HorizontalSlider(new Rect(140, 260, 180, 20), predictionTime, 0.05f, 0.5f);
                GUI.Label(new Rect(330, 255, 80, 20), predictionTime.ToString("F2") + "s");
            }

            // --- Active Collisions List ---
            GUI.Box(new Rect(10, 310, 410, 250), $"活动碰撞 ({activeCollisions.Count})");
            scrollPosition = GUI.BeginScrollView(new Rect(20, 335, 390, 215), scrollPosition, new Rect(0, 0, 370, activeCollisions.Count * 25));
            int i = 0;
            foreach (var collision in activeCollisions.Values)
            {
                string info = $"{collision.bone.name} - 深度: {collision.penetrationDepth:F3}";
                if (GUI.Button(new Rect(0, i * 25, 370, 20), info))
                {
                    selectedBone = collision.bone;
                    manualOffset = Vector3.zero;
                    if (!originalLocalPositions.ContainsKey(collision.bone))
                        originalLocalPositions[collision.bone] = collision.bone.localPosition;
                }
                i++;
            }
            GUI.EndScrollView();

            // --- Visualization Controls ---
            GUI.Box(new Rect(10, 570, 410, 80), "可视化");
            showCollisionMarkers = GUI.Toggle(new Rect(20, 595, 150, 20), showCollisionMarkers, "显示碰撞标记");
            showPredictionMarkers = GUI.Toggle(new Rect(180, 595, 150, 20), showPredictionMarkers, "显示预测标记");
            showDebugInfo = GUI.Toggle(new Rect(20, 620, 150, 20), showDebugInfo, "显示调试信息");
        }

        void DrawStrategyTab()
        {
            // --- Anti-Clipping Mode ---
            GUI.Box(new Rect(10, 60, 410, 120), "防穿模策略");
            GUI.Label(new Rect(20, 85, 100, 20), "当前模式:");

            string[] modeNames = { "推力", "拉力", "约束", "混合", "物理" };
            int newMode = GUI.SelectionGrid(new Rect(20, 110, 390, 60), (int)currentMode, modeNames, 3);
            currentMode = (AntiClippingMode)newMode;

            // --- Auto Correction Settings ---
            GUI.Box(new Rect(10, 190, 410, 150), "自动修正设置");
            autoCorrectionEnabled = GUI.Toggle(new Rect(20, 215, 150, 20), autoCorrectionEnabled, "启用自动修正");

            GUI.Label(new Rect(20, 240, 100, 20), "推力强度:");
            pushForce = GUI.HorizontalSlider(new Rect(120, 245, 200, 20), pushForce, 0.001f, 0.1f);
            GUI.Label(new Rect(330, 240, 80, 20), pushForce.ToString("F3"));

            GUI.Label(new Rect(20, 265, 100, 20), "拉力强度:");
            pullForce = GUI.HorizontalSlider(new Rect(120, 270, 200, 20), pullForce, 0.001f, 0.1f);
            GUI.Label(new Rect(330, 265, 80, 20), pullForce.ToString("F3"));

            GUI.Label(new Rect(20, 290, 100, 20), "约束刚度:");
            constraintStiffness = GUI.HorizontalSlider(new Rect(120, 295, 200, 20), constraintStiffness, 0.1f, 1.0f);
            GUI.Label(new Rect(330, 290, 80, 20), constraintStiffness.ToString("F2"));

            GUI.Label(new Rect(20, 315, 100, 20), "阻尼系数:");
            dampingFactor = GUI.HorizontalSlider(new Rect(120, 320, 200, 20), dampingFactor, 0.8f, 0.99f);
            GUI.Label(new Rect(330, 315, 80, 20), dampingFactor.ToString("F2"));

            // --- Manual Adjustment ---
            if (selectedBone != null)
            {
                GUI.Box(new Rect(10, 350, 410, 150), $"手动调整: {selectedBone.name}");
                manualOffset = DrawVector3Slider(new Rect(20, 375, 390, 75), manualOffset);
                if (GUI.Button(new Rect(20, 460, 390, 25), "重置位置"))
                {
                    if (originalLocalPositions.ContainsKey(selectedBone))
                        selectedBone.localPosition = originalLocalPositions[selectedBone];
                    manualOffset = Vector3.zero;
                    selectedBone = null;
                }
            }
            else
            {
                GUI.Box(new Rect(10, 350, 410, 50), "手动调整");
                GUI.Label(new Rect(20, 375, 390, 20), "从检测标签页选择一个骨骼进行手动调整");
            }
        }

        void DrawConstraintTab()
        {
            // --- Physics Constraints ---
            GUI.Box(new Rect(10, 60, 410, 120), "物理约束");
            enablePhysicsConstraints = GUI.Toggle(new Rect(20, 85, 150, 20), enablePhysicsConstraints, "启用物理约束");
            enableDistanceConstraints = GUI.Toggle(new Rect(20, 110, 150, 20), enableDistanceConstraints, "距离约束");
            enableSpringConstraints = GUI.Toggle(new Rect(200, 110, 150, 20), enableSpringConstraints, "弹簧约束");

            GUI.Label(new Rect(20, 135, 120, 20), "最大约束距离:");
            maxConstraintDistance = GUI.HorizontalSlider(new Rect(140, 140, 180, 20), maxConstraintDistance, 0.01f, 0.2f);
            GUI.Label(new Rect(330, 135, 80, 20), maxConstraintDistance.ToString("F3"));

            // --- Distance Constraints List ---
            GUI.Box(new Rect(10, 190, 410, 150), $"距离约束 ({distanceConstraints.Count})");
            if (GUI.Button(new Rect(20, 215, 100, 20), "添加约束"))
            {
                if (selectedBone != null && activeCollisions.ContainsKey(selectedBone))
                {
                    var collision = activeCollisions[selectedBone];
                    AddDistanceConstraint(selectedBone, collision.collider.transform);
                }
            }
            if (GUI.Button(new Rect(130, 215, 100, 20), "清除所有"))
            {
                distanceConstraints.Clear();
            }

            // List distance constraints
            for (int i = 0; i < distanceConstraints.Count && i < 5; i++)
            {
                var constraint = distanceConstraints[i];
                string info = $"{constraint.boneA?.name} <-> {constraint.boneB?.name} ({constraint.restDistance:F3})";
                GUI.Label(new Rect(20, 240 + i * 20, 300, 20), info);
                constraint.isActive = GUI.Toggle(new Rect(330, 240 + i * 20, 80, 20), constraint.isActive, "激活");
            }

            // --- Spring Constraints List ---
            GUI.Box(new Rect(10, 350, 410, 150), $"弹簧约束 ({springConstraints.Count})");
            if (GUI.Button(new Rect(20, 375, 100, 20), "添加弹簧"))
            {
                if (selectedBone != null)
                {
                    AddSpringConstraint(selectedBone, selectedBone.position);
                }
            }
            if (GUI.Button(new Rect(130, 375, 100, 20), "清除所有"))
            {
                springConstraints.Clear();
            }

            // List spring constraints
            for (int i = 0; i < springConstraints.Count && i < 5; i++)
            {
                var constraint = springConstraints[i];
                string info = $"{constraint.bone?.name} -> 锚点 ({constraint.springConstant:F1})";
                GUI.Label(new Rect(20, 400 + i * 20, 300, 20), info);
                constraint.isActive = GUI.Toggle(new Rect(330, 400 + i * 20, 80, 20), constraint.isActive, "激活");
            }
        }

        void DrawDebugTab()
        {
            // --- Performance Info ---
            GUI.Box(new Rect(10, 60, 410, 120), "性能信息");
            GUI.Label(new Rect(20, 85, 200, 20), $"活动碰撞: {activeCollisions.Count}");
            GUI.Label(new Rect(220, 85, 200, 20), $"预测碰撞: {predictedCollisions.Count}");
            GUI.Label(new Rect(20, 110, 200, 20), $"目标碰撞器: {targetColliders.Count}");
            GUI.Label(new Rect(220, 110, 200, 20), $"粒子数量: {clippingParticles.Count}");
            GUI.Label(new Rect(20, 135, 200, 20), $"距离约束: {distanceConstraints.Count}");
            GUI.Label(new Rect(220, 135, 200, 20), $"弹簧约束: {springConstraints.Count}");
            GUI.Label(new Rect(20, 160, 200, 20), $"FPS: {(1f / Time.deltaTime):F1}");

            // --- Debug Controls ---
            GUI.Box(new Rect(10, 190, 410, 100), "调试控制");
            if (GUI.Button(new Rect(20, 215, 120, 25), "重置所有位置"))
            {
                ResetAllBonePositions();
            }
            if (GUI.Button(new Rect(150, 215, 120, 25), "清除所有约束"))
            {
                distanceConstraints.Clear();
                springConstraints.Clear();
            }
            if (GUI.Button(new Rect(280, 215, 120, 25), "重新初始化"))
            {
                InitializeSystem();
            }

            if (GUI.Button(new Rect(20, 250, 120, 25), "保存设置"))
            {
                SaveSettings();
            }
            if (GUI.Button(new Rect(150, 250, 120, 25), "加载设置"))
            {
                LoadSettings();
            }

            // --- Real-time Debug Info ---
            if (showDebugInfo && selectedBone != null)
            {
                GUI.Box(new Rect(10, 300, 410, 200), $"调试信息: {selectedBone.name}");
                Vector3 pos = selectedBone.position;
                GUI.Label(new Rect(20, 325, 390, 20), $"位置: ({pos.x:F3}, {pos.y:F3}, {pos.z:F3})");

                if (boneVelocities.ContainsKey(selectedBone))
                {
                    Vector3 vel = boneVelocities[selectedBone];
                    GUI.Label(new Rect(20, 350, 390, 20), $"速度: ({vel.x:F3}, {vel.y:F3}, {vel.z:F3})");
                }

                if (activeCollisions.ContainsKey(selectedBone))
                {
                    var collision = activeCollisions[selectedBone];
                    GUI.Label(new Rect(20, 375, 390, 20), $"穿透深度: {collision.penetrationDepth:F4}");
                    GUI.Label(new Rect(20, 400, 390, 20), $"碰撞器: {collision.collider.name}");
                    GUI.Label(new Rect(20, 425, 390, 20), $"碰撞类型: {collision.type}");
                }
            }
        }

        void SetObject(bool isTarget)
        {
            // 使用反射安全地获取选中的角色对象
            OCIChar ociChar = null;
            try
            {
                // 获取Studio实例
                var studioInstance = Studio.Studio.Instance;
                if (studioInstance == null) return;
                
                // 获取treeNodeCtrl
                var treeNodeCtrl = studioInstance.treeNodeCtrl;
                if (treeNodeCtrl == null) return;
                
                // 获取selectNode
                var selectNode = treeNodeCtrl.selectNode;
                if (selectNode == null) return;
                
                // 使用反射获取objectInfo属性
                var objectInfoProp = selectNode.GetType().GetProperty("objectInfo");
                if (objectInfoProp != null)
                {
                    var objectInfo = objectInfoProp.GetValue(selectNode, null);
                    if (objectInfo is OCIChar)
                    {
                        ociChar = (OCIChar)objectInfo;
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.LogError($"获取选中对象失败: {ex.Message}");
                return;
            }
            
            if (ociChar == null) return;

            if (isTarget)
            {
                targetBodyChar = ociChar;
                targetColliders = targetBodyChar.charInfo.GetComponentsInChildren<DynamicBoneCollider>().ToList();
            }
            else
            {
                clippingItemChar = ociChar;
                var dynamicBones = clippingItemChar.charInfo.GetComponentsInChildren<DynamicBone>();
                this.dynamicBones = dynamicBones.ToList();
                clippingParticles.Clear();

                // Use reflection to access private m_Particles field
                foreach (var db in dynamicBones)
                {
                    if (particlesField != null)
                    {
                        var particles = particlesField.GetValue(db) as System.Collections.IList;
                        if (particles != null)
                        {
                            foreach (var particle in particles)
                                clippingParticles.Add(particle);
                        }
                    }
                }

                // Initialize system after setting objects
                if (targetBodyChar != null && clippingItemChar != null)
                    InitializeSystem();
            }
        }

        void InitializeSystem()
        {
            isInitialized = true;
            spatialGrid = new SpatialGrid(0.1f);

            // Store original positions
            originalLocalPositions.Clear();
            foreach (var particle in clippingParticles)
            {
                Transform particleTransform = GetParticleTransform(particle);
                if (particleTransform != null)
                    originalLocalPositions[particleTransform] = particleTransform.localPosition;
            }
        }

        void AddDistanceConstraint(Transform boneA, Transform boneB)
        {
            if (boneA == null || boneB == null) return;

            float distance = Vector3.Distance(boneA.position, boneB.position);
            var constraint = new DistanceConstraint
            {
                boneA = boneA,
                boneB = boneB,
                restDistance = distance,
                stiffness = constraintStiffness,
                isActive = true
            };

            distanceConstraints.Add(constraint);
        }

        void AddSpringConstraint(Transform bone, Vector3 anchorPoint)
        {
            if (bone == null) return;

            var constraint = new SpringConstraint
            {
                bone = bone,
                anchorPoint = anchorPoint,
                springConstant = 50f,
                damping = 0.9f,
                restLength = 0.01f,
                isActive = true
            };

            springConstraints.Add(constraint);
        }

        void ResetAllBonePositions()
        {
            foreach (var kvp in originalLocalPositions)
            {
                if (kvp.Key != null)
                    kvp.Key.localPosition = kvp.Value;
            }

            boneVelocities.Clear();
            selectedBone = null;
            manualOffset = Vector3.zero;
        }

        void SaveSettings()
        {
            // Simple settings save - could be enhanced with PlayerPrefs or file I/O
            PlayerPrefs.SetFloat("CollisionGuard_PushForce", pushForce);
            PlayerPrefs.SetFloat("CollisionGuard_PullForce", pullForce);
            PlayerPrefs.SetFloat("CollisionGuard_ConstraintStiffness", constraintStiffness);
            PlayerPrefs.SetFloat("CollisionGuard_DampingFactor", dampingFactor);
            PlayerPrefs.SetFloat("CollisionGuard_DetectionRadius", detectionRadius);
            PlayerPrefs.SetFloat("CollisionGuard_PredictionTime", predictionTime);
            PlayerPrefs.SetInt("CollisionGuard_Mode", (int)currentMode);
            PlayerPrefs.SetInt("CollisionGuard_AutoCorrection", autoCorrectionEnabled ? 1 : 0);
            PlayerPrefs.SetInt("CollisionGuard_PredictiveDetection", enablePredictiveDetection ? 1 : 0);
        }

        void LoadSettings()
        {
            pushForce = PlayerPrefs.GetFloat("CollisionGuard_PushForce", 0.02f);
            pullForce = PlayerPrefs.GetFloat("CollisionGuard_PullForce", 0.015f);
            constraintStiffness = PlayerPrefs.GetFloat("CollisionGuard_ConstraintStiffness", 0.8f);
            dampingFactor = PlayerPrefs.GetFloat("CollisionGuard_DampingFactor", 0.95f);
            detectionRadius = PlayerPrefs.GetFloat("CollisionGuard_DetectionRadius", 0.1f);
            predictionTime = PlayerPrefs.GetFloat("CollisionGuard_PredictionTime", 0.1f);
            currentMode = (AntiClippingMode)PlayerPrefs.GetInt("CollisionGuard_Mode", (int)AntiClippingMode.Hybrid);
            autoCorrectionEnabled = PlayerPrefs.GetInt("CollisionGuard_AutoCorrection", 1) == 1;
            enablePredictiveDetection = PlayerPrefs.GetInt("CollisionGuard_PredictiveDetection", 1) == 1;
        }

        // 删除残留的旧可视化方法

        Vector3 DrawVector3Slider(Rect rect, Vector3 value)
        {
            GUI.BeginGroup(rect);
            GUI.Label(new Rect(0, 0, 20, 20), "X");
            value.x = GUI.HorizontalSlider(new Rect(25, 5, 315, 20), value.x, -0.1f, 0.1f);
            GUI.Label(new Rect(0, 25, 20, 20), "Y");
            value.y = GUI.HorizontalSlider(new Rect(25, 30, 315, 20), value.y, -0.1f, 0.1f);
            GUI.Label(new Rect(0, 50, 20, 20), "Z");
            value.z = GUI.HorizontalSlider(new Rect(25, 55, 315, 20), value.z, -0.1f, 0.1f);
            GUI.EndGroup();

            if (selectedBone != null) selectedBone.localPosition = originalLocalPositions[selectedBone] + value;

            return value;
        }

        void OnDestroy()
        {
            // 清理方法已删除
        }
    }
}
