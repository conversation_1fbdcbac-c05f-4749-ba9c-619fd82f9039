
# HS2 Anti-Clipping API Deep Dive & Developer Guide

**Version: 1.0**

**Target Audience: Plugin Developers**

## 1. Introduction

This document provides an exhaustive technical guide to the Application Programming Interfaces (APIs) related to collision and clipping prevention in HS2. It is intended for developers who wish to build advanced tools on top of the game's native `DynamicBone` system.

Unlike the previous guides, this document will focus on the low-level components and methods, explaining their properties and how they interact to form the game's physics simulation for clothing, hair, and accessories.

---

## 2. System Architecture: The `DynamicBone` Ecosystem

At its core, all soft-body physics in HS2 are managed by one system. Understanding its components is the key to manipulating it.

*   **`DynamicBone`**: This is the main script that drives a chain of bones (a `Transform` hierarchy). Think of it as the "engine". It's attached to the root bone of a skirt, a ponytail, etc. It contains a list of "particles" that it simulates.

*   **`DynamicBone.Particle`**: This is not a component, but a class structure *within* `DynamicBone`. Each `Particle` represents a single `Transform` (a bone) in the chain. These are the objects that actually move and are checked for collisions.

*   **`DynamicBoneCollider` & `DynamicBoneColliderBase`**: These are the "barriers". They are components attached to solid body parts (like thighs, torso, head). `DynamicBone.Particle` objects collide with these `DynamicBoneCollider` volumes.

**The fundamental interaction is: `DynamicBone` moves its `Particles`, and if a `Particle` hits a `DynamicBoneCollider`, its position is corrected.**

---

## 3. API Deep Dive: Component Properties

Here are the most critical properties you will need to access and modify.

### 3.1. `DynamicBone` Component

| Property | Type | Description & Use Case |
| :--- | :--- | :--- |
| `m_Root` | `Transform` | The root of the bone chain. **Use Case**: Identifying which object this `DynamicBone` script belongs to (e.g., `m_Root.name` might be "skirt_root"). |
| `m_Particles` | `List<Particle>` | **The most important property.** This list contains every simulated point in the chain. You will iterate through this to check or modify individual bone positions. |
| `m_Colliders` | `List<DynamicBoneColliderBase>` | The list of colliders this bone chain should interact with. **Use Case**: You can programmatically add or remove colliders from this list to enable/disable collision with specific body parts. |
| `m_Stiffness` | `float` | Controls how much the bones try to return to their original shape. **Use Case**: Increasing stiffness can help prevent clipping by making a skirt "harder". |
| `m_Elasticity` | `float` | Controls how much momentum is conserved after deformation. **Use Case**: Can be tweaked alongside stiffness for more rigid or bouncy behavior. |
| `m_Radius` | `float` | A base radius for all particles in this chain. **Use Case**: Increasing this can create a larger buffer zone around bones, preventing them from getting too close to colliders. |

### 3.2. `DynamicBone.Particle` Structure

This is the data structure for each point in the `m_Particles` list.

| Property | Type | Description & Use Case |
| :--- | :--- | :--- |
| `m_Transform` | `Transform` | The actual bone `Transform` in the scene. **This is your handle to modify the bone's position, rotation, etc.** |
| `m_Position` | `Vector3` | The current world-space position of the particle in the physics simulation. This is the value you should read. |
| `m_Radius` | `float` | The individual radius of this specific particle. It's combined with the `DynamicBone`'s `m_Radius`. |
| `m_InitLocalPosition` | `Vector3` | The bone's original, unmodified local position. **Use Case**: Essential for resetting any manual changes you've made. |

### 3.3. `DynamicBoneCollider` Component

This defines the collision volume.

| Property | Type | Description & Use Case |
| :--- | :--- | :--- |
| `m_Center` | `Vector3` | The center of the collider volume, in the `Transform`'s local space. **Crucial for all calculations.** |
| `m_Radius` | `float` | The radius of the sphere or the capsule ends. |
| `m_Height` | `float` | **If 0, the collider is a sphere. If > 0, it's a capsule.** This is how you distinguish them. |
| `m_Direction` | `Direction` (enum) | For capsules, this defines its primary axis (X, Y, or Z) in local space. |
| `m_Bound` | `Bound` (enum) | Defines if particles are kept `Outside` or `Inside` the volume. For anti-clipping, you only care about `Outside`. |

---

## 4. The Core Collision Method: `Collide()`

While `Inside()` is useful for a simple check, the real workhorse is `Collide()`. This is the single most important API for any anti-clipping tool.

**Location**: `DynamicBoneColliderBase` (the parent class of `DynamicBoneCollider`)

**Signature**:
`public bool Collide(ref Vector3 particlePosition, float particleRadius)`

**Explanation**:

*   **`ref Vector3 particlePosition`**: This is the key. The method takes the particle's position **by reference**. If a collision is detected, **this method directly modifies the `particlePosition` variable you passed in**, moving it to the corrected spot on the surface of the collider.
*   **`float particleRadius`**: The radius of the particle being checked.
*   **`returns bool`**: Returns `true` if a collision occurred and the position was modified, `false` otherwise.

**How to use it correctly**:

```csharp
// p is a DynamicBone.Particle
// c is a DynamicBoneCollider

Vector3 currentPos = p.m_Transform.position; // Get the current position

// Pass the position BY REFERENCE to the Collide method
if (c.Collide(ref currentPos, p.m_Radius))
{
    // If Collide() returns true, it means 'currentPos' has been MODIFIED.
    // Now, apply the corrected position back to the actual bone.
    p.m_Transform.position = currentPos;

    // You can now log this event or flag this bone as corrected.
    Debug.Log($"Collision corrected for bone {p.m_Transform.name}!");
}
```

---

## 5. Full Instance Template: `AdvancedCollisionDebugger.cs`

Here is a complete, heavily-commented BepInEx plugin that serves as a practical template. It finds all `DynamicBone` systems on a selected character and manually runs the collision checks, logging every correction. Developers can use this as a starting point for their own tools.

```csharp
using BepInEx;
using UnityEngine;
using Studio;
using AIChara;
using System.Collections.Generic;
using System.Linq;

namespace StudioDeveloperTools
{
    [BepInPlugin("com.example.advancedcollisiondebugger", "Advanced Collision Debugger", "1.0.0")]
    public class AdvancedCollisionDebugger : BaseUnityPlugin
    {
        private bool isVisible = false;
        private Rect windowRect = new Rect(20, 20, 450, 200);
        private OCIChar selectedChar;

        void Update()
        {
            // Use a different key to avoid conflict with other tools
            if (Input.GetKeyDown(KeyCode.F5))
            {
                isVisible = !isVisible;
            }
        }

        void OnGUI()
        {
            if (!isVisible) return;
            windowRect = GUI.Window(10, windowRect, DrawWindow, "Advanced Collision Debugger");
        }

        void DrawWindow(int windowID)
        {
            GUI.DragWindow(new Rect(0, 0, 10000, 20));

            // Get currently selected character in Studio
            selectedChar = Studio.Studio.Instance.treeNodeCtrl.selectNode?.data as OCIChar;

            if (selectedChar == null)
            {
                GUI.Label(new Rect(10, 25, 430, 20), "Please select a character in the Studio scene.");
                return;
            }

            GUI.Label(new Rect(10, 25, 430, 20), $"Target: {selectedChar.charInfo.chaFile.parameter.fullname}");

            GUI.Label(new Rect(10, 50, 430, 40), "This tool manually re-runs the game's native collision checks. Open the BepInEx console (F12) to see the output.");

            if (GUI.Button(new Rect(10, 100, 430, 80), "Run Manual Collision Check & Fix on Selected Character"))
            {
                RunCollisionCheckAndFix(selectedChar.charInfo);
            }
        }

        /// <summary>
        /// This is the core demonstration method. It finds all dynamic bones and colliders
        /// on a character and manually performs the collision checks.
        /// </summary>
        /// <param name="chaControl">The ChaControl of the target character.</param>
        public void RunCollisionCheckAndFix(ChaControl chaControl)
        {
            if (chaControl == null) return;

            Logger.LogInfo("--- Starting Manual Collision Check --- ");

            // 1. Get all colliders on the character. These are our "barriers".
            var allColliders = chaControl.GetComponentsInChildren<DynamicBoneCollider>(true).ToList();
            Logger.LogInfo($"Found {allColliders.Count} DynamicBoneColliders on {chaControl.chaFile.parameter.fullname}.");

            // 2. Get all DynamicBone systems on the character. These are our "movers".
            var allDynamicBones = chaControl.GetComponentsInChildren<DynamicBone>(true).ToList();
            Logger.LogInfo($"Found {allDynamicBones.Count} DynamicBone systems.");

            int totalCorrections = 0;

            // 3. Iterate through each DynamicBone system (each skirt, hair piece, etc.)
            foreach (var db in allDynamicBones)
            {
                if (db.m_Root == null) continue;
                //Logger.LogInfo($"Checking system: {db.m_Root.name}");

                // 4. Iterate through every particle (bone) in that system
                foreach (var particle in db.m_Particles)
                {
                    if (particle.m_Transform == null) continue;

                    Vector3 particlePos = particle.m_Transform.position;
                    float particleRadius = particle.m_Radius + db.m_Radius; // Total radius

                    // 5. Check this single particle against ALL colliders on the character
                    foreach (var collider in allColliders)
                    {
                        // 6. Use the core Collide() method. It will modify particlePos if needed.
                        if (collider.Collide(ref particlePos, particleRadius))
                        {
                            // 7. If a collision was detected and corrected, apply the new position.
                            particle.m_Transform.position = particlePos;
                            totalCorrections++;
                            Logger.LogWarning($"Corrected clipping! Bone '{particle.m_Transform.name}' was inside '{collider.name}'. Applied fix.");
                        }
                    }
                }
            }

            Logger.LogInfo($"--- Manual Collision Check Finished. Total corrections made: {totalCorrections} ---");
        }
    }
}
```
