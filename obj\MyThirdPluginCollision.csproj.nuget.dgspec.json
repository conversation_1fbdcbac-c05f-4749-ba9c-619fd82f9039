{"format": 1, "restore": {"E:\\Plugins DEV\\MyThirdPluginCollision\\MyThirdPluginCollision.csproj": {}}, "projects": {"E:\\Plugins DEV\\MyThirdPluginCollision\\MyThirdPluginCollision.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\Plugins DEV\\MyThirdPluginCollision\\MyThirdPluginCollision.csproj", "projectName": "MyThirdPluginCollision", "projectPath": "E:\\Plugins DEV\\MyThirdPluginCollision\\MyThirdPluginCollision.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\Plugins DEV\\MyThirdPluginCollision\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net46"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.bepinex.dev/v3/index.json": {}, "https://nuget.samboy.dev/v3/index.json": {}}, "frameworks": {"net46": {"targetAlias": "net46", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net46": {"targetAlias": "net46", "dependencies": {"BepInEx.Analyzers": {"suppressParent": "All", "target": "Package", "version": "[1.*, )"}, "BepInEx.Core": {"target": "Package", "version": "[5.*, )"}, "BepInEx.PluginInfoProps": {"target": "Package", "version": "[2.*, )"}, "Microsoft.NETFramework.ReferenceAssemblies": {"suppressParent": "All", "target": "Package", "version": "[1.0.2, )"}, "UnityEngine.Modules": {"include": "Compile", "target": "Package", "version": "[2018.4.11, )"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.401\\RuntimeIdentifierGraph.json"}}}}}